# -*- coding: utf-8 -*-
import logging
import json
import requests
import warnings
import os
import subprocess
from io import Bytes<PERSON>
from werkzeug.datastructures import FileStorage
from tempfile import NamedTemporaryFile
from odoo import http, _, SUPERUSER_ID
from odoo.http import request, Response
from odoo.exceptions import UserError
from odoo.addons.base.models.ir_qweb import Q<PERSON>ebException
from psycopg2 import OperationalError
from psycopg2.extensions import TransactionRollbackError
from ..Helpers import MessagesHandler
from ..models.Message import INSTAGRAM_AUDIO_FORMAT_ALLOWED, INSTAGRAM_VIDEO_FORMAT_ALLOWED
from ..models.WcMessage import WECHAT_AUDIO_FORMAT_ALLOWED, WECHAT_VIDEO_FORMAT_ALLOWED

_logger = logging.getLogger(__name__)

try:
    saved_warning_state = warnings.filters[:]
    warnings.simplefilter('ignore')
    import pydub
except Exception:
    pydub = None
finally:
    warnings.filters = saved_warning_state


def log_request(req):
    _logger.info("Request Headers: %s", req.httprequest.headers)  # Log the headers
    _logger.info("Request Data: %s", req.httprequest.data)


def acrux_allowed_models():
    return ['product.template', 'product.product', 'acrux.chat.new.group.wizard', 'acrux.chat.conversation']


class WebhookController(http.Controller):

    @http.route('/acrux_webhook/test', auth='public', type='http')
    def acrux_webhook_test(self, **post):
        return Response("Webhook test endpoint is working", status=200, content_type='text/plain')

    @http.route('/acrux_webhook/whatsapp_test/<string:connector_uuid>',
                auth='public', type='http', csrf=False, methods=['GET', 'POST'])
    def acrux_webhook_whatsapp_test(self, connector_uuid, **kwargs):
        ''' Test endpoint for WhatsApp webhook connectivity '''
        try:
            _logger.info("🧪 WhatsApp webhook test called for connector: %s", connector_uuid)

            # Find connector
            Connector = request.env['acrux.chat.connector'].sudo()
            connector = Connector.search([('uuid', '=', connector_uuid)], limit=1)

            if not connector:
                return request.make_response(
                    f"❌ Connector not found for UUID: {connector_uuid}",
                    [('Content-Type', 'text/plain')],
                    404
                )

            test_info = {
                "status": "✅ Webhook endpoint is reachable",
                "connector_uuid": connector_uuid,
                "connector_name": connector.name,
                "connector_id": connector.id,
                "method": request.httprequest.method,
                "timestamp": str(request.env.cr.now()),
                "headers": dict(request.httprequest.headers),
                "args": dict(request.httprequest.args) if request.httprequest.args else {},
            }

            if request.httprequest.method == "POST":
                try:
                    if hasattr(request.httprequest, 'json') and request.httprequest.json:
                        test_info["json_data"] = request.httprequest.json
                    else:
                        raw_data = request.httprequest.data
                        if raw_data:
                            test_info["raw_data"] = raw_data.decode('utf-8')
                except Exception as e:
                    test_info["data_error"] = str(e)

            return request.make_response(
                json.dumps(test_info, indent=2),
                [('Content-Type', 'application/json')]
            )

        except Exception as e:
            _logger.error('❌ Webhook test error: %s', str(e), exc_info=True)
            return request.make_response(
                f"❌ Test failed: {str(e)}",
                [('Content-Type', 'text/plain')],
                500
            )

    @http.route('/connector/create', auth='public', type='http', csrf=False, methods=['POST'])
    def create_chat_connector(self, **kwargs):
        try:
            raw_data = request.httprequest.data
            data = json.loads(raw_data.decode('utf-8'))
            required_fields = ['name', 'connector_type', 'source', 'token', 'uuid', 'odoo_url', 'endpoint']
            connector_vals = {field: data.get(field) for field in required_fields}
            missing = [f for f in required_fields if not connector_vals.get(f)]
            if missing:
                return Response(
                    json.dumps({"error": f"Missing required fields: {', '.join(missing)}"}),
                    status=400,
                    content_type='application/json'
                )

            # Try to find existing connector by name OR uuid
            existing = request.env['acrux.chat.connector'].sudo().search([
                '|',
                ('name', '=', connector_vals['name']),
                ('uuid', '=', connector_vals['uuid']),
            ], limit=1)

            if existing:
                # Update the token of existing connector
                existing.sudo().write({'token': connector_vals['token']})
                return Response(json.dumps({
                    "success": True,
                    "message": "Existing connector updated with new token",
                    "connector_id": existing.id,
                    "name": existing.name
                }), status=200, content_type='application/json')

            # If not existing, create new connector
            connector_vals['company_id'] = request.env.company.id
            connector = request.env['acrux.chat.connector'].sudo().create(connector_vals)
            return Response(json.dumps({"success": True, "connector_id": connector.id, "name": connector.name}),
                            status=200,
                            content_type='application/json')

        except Exception as e:
            _logger.exception("Error creating chat connector")
            return Response(json.dumps({"error": f"Unexpected error: {str(e)}"}),
                            status=500,
                            content_type='application/json')

    @http.route('/acrux_webhook/whatsapp_connector/<string:connector_uuid>/process',
                auth='public', type='json', methods=['POST'])
    def acrux_process(self, connector_uuid, **post):
        ''' Keeping "Account ID" secret. '''
        log_request(request)
        Connector = request.env['acrux.chat.connector'].with_user(SUPERUSER_ID).sudo()
        connector_id = Connector.search([('uuid', '=', connector_uuid)], limit=1)
        if not connector_id or not connector_uuid:
            return Response(status=403)  # Forbidden

        WorkQueue = request.env['acrux.chat.work.queue'].with_user(SUPERUSER_ID).sudo()
        WorkQueue._cron_process_queue([connector_id.id])

        return Response(status=200)

    @http.route('/acrux_webhook/messenger_connector', csrf=False, auth='public', type='http',
                methods=['GET', 'POST'])
    def messenger_webhook(self):
        log_request(request)

        if request.httprequest.method == "GET":
            mode = request.httprequest.args.get("hub.mode")
            token = request.httprequest.args.get("hub.verify_token")
            challenge = request.httprequest.args.get("hub.challenge")

            # Validate the request
            if mode == "subscribe" and token == "TOKEN":
                return request.make_response(challenge, [('Content-Type', 'text/plain')])
            return request.make_response("Verification failed", 403)

        elif request.httprequest.method == "POST":
            data = request.httprequest.json
            messages_handler = MessagesHandler()
            messages_handler.handle_meta_messages(data)

            WorkQueue = request.env['acrux.chat.work.queue'].with_user(SUPERUSER_ID).sudo()
            WorkQueue._cron_process_queue()

            return request.make_response("OK", [('Content-Type', 'text/plain')])

    @http.route('/acrux_webhook/instagram_connector', csrf=False, auth='public', type='http',
                methods=['GET', 'POST'])
    def webhook(self):
        log_request(request)

        if request.httprequest.method == "GET":  # Facebook Verification Request
            mode = request.httprequest.args.get("hub.mode")
            token = request.httprequest.args.get("hub.verify_token")
            challenge = request.httprequest.args.get("hub.challenge")

            # Validate the request
            if mode == "subscribe" and token == "TOKEN":  # Ensure "TOKEN" matches Facebook settings
                return request.make_response(challenge, [('Content-Type', 'text/plain')])  # Return plain text response
            return request.make_response("Verification failed", 403)

        elif request.httprequest.method == "POST":
            data = request.httprequest.json
            messages_handler = MessagesHandler()
            messages_handler.handle_meta_messages(data)

            WorkQueue = request.env['acrux.chat.work.queue'].with_user(SUPERUSER_ID).sudo()
            WorkQueue._cron_process_queue()
            return request.make_response("OK", [('Content-Type', 'text/plain')])

    @http.route('/meta_oauth/callback', csrf=False, auth='public', type='http', methods=['GET'])
    def meta_oauth_callback(self, **kwargs):
        """
        OAuth callback for Facebook/Instagram. This receives the `code` from Meta and exchanges it
        for an access token. You can then save it or use it to fetch IG account info.
        """
        code = kwargs.get('code')
        error = kwargs.get('error')

        if error:
            _logger.error(f'OAuth error: {error}')
            return Response("OAuth failed", status=400)

        if not code:
            _logger.warning('No code received from Meta OAuth')
            return Response("Missing authorization code", status=400)

        try:
            # Exchange code for access token
            app_id = '****************'
            app_secret = '7f57c748e2d77e0fa2788f2d37b25d4a'

            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            redirect_uri = base_url + '/meta_oauth/callback'

            token_url = 'https://graph.facebook.com/v22.0/oauth/access_token'
            params = {
                'client_id': app_id,
                'redirect_uri': redirect_uri,
                'client_secret': app_secret,
                'code': code
            }

            res = requests.get(token_url, params=params)
            token_data = res.json()

            if 'access_token' not in token_data:
                _logger.error(f'Access token response error: {token_data}')
                return Response("Error getting access token", status=500)

            access_token = token_data['access_token']
            _logger.info(f'Access token received: {access_token}')

            return self.subscribe_to_webhooks(access_token)


        except Exception as e:
            _logger.error('OAuth callback error', exc_info=True)
            request.env.cr.rollback()
            return Response("Internal server error", status=500)

    def subscribe_to_webhooks(self, access_token):
        pages_url = 'https://graph.facebook.com/v22.0/me/accounts'
        pages_response = requests.get(pages_url, params={'access_token': access_token}).json()

        if 'data' not in pages_response or not pages_response['data']:
            _logger.error("❌ No pages found for this access token")
            _logger.error("📄 Response: %s", json.dumps(pages_response, indent=2))
            return {
                "error": "No pages found. Make sure the user is an admin on at least one Page and has granted all permissions."}, 400

        # Prepare pages data for rendering
        pages = [
            {
                "id": page["id"],
                "name": page.get("name", ""),
                "access_token": page["access_token"]
            }
            for page in pages_response["data"]
        ]

        # Render the HTML page with checkboxes
        return request.render('whatsapp_connector.page_with_checkboxes', {
            'pages': pages
        })

    def update_connector_token(self, page_name, page_id, token, connector_type):
        Connectors = request.env['acrux.chat.connector'].sudo()
        connector = Connectors.search([
            ('connector_type', 'in', ['instagram', 'facebook']),
            ('uuid', '=', page_id)
        ], limit=1)

        if connector:
            connector.write({'token': token})
        else:
            Connectors.create({
                'name': page_name,
                'connector_type': connector_type,
                'uuid': page_id,
                'token': token,
                'source': '/',
            })

    @http.route('/meta_oauth/subscribe_pages', type='http', auth='user', methods=['POST'], csrf=True)
    def subscribe_selected_pages(self, **post):
        selected_ids = request.httprequest.form.getlist('page_ids')
        selected_names = request.httprequest.form.getlist('page_names')
        all_tokens = request.httprequest.form.getlist('page_tokens')

        _logger.info(f"✅ Selected Page IDs: {selected_ids}")
        _logger.info(f"🔑 Access Tokens Received: {all_tokens}")

        selected_pages = list(zip(selected_ids, selected_names, all_tokens))

        for page_id, page_name, access_token, in selected_pages:
            _logger.info(f"📡 Subscribing page {page_id} with token {access_token}")
            sub_url = f'https://graph.facebook.com/v22.0/{page_id}/subscribed_apps'
            sub_response = requests.post(sub_url, params={
                'subscribed_fields': 'messages,message_echoes',
                'access_token': access_token
            }).json()

            # Check if the page has an Instagram account connected
            ig_check_url = f'https://graph.facebook.com/v22.0/{page_id}?fields=connected_instagram_account&access_token={access_token}'
            ig_response = requests.get(ig_check_url).json()
            instagram_account = ig_response.get("connected_instagram_account")

            # Update or create connector for Facebook page
            self.update_connector_token(page_name, page_id, access_token, 'facebook')

            # If Instagram account connected, update or create connector for Instagram as well
            if instagram_account:
                instagram_id = instagram_account.get("id")
                if instagram_id:
                    self.update_connector_token(page_name + ' - Instagram', instagram_id, access_token, 'instagram')

            if sub_response.get('success'):
                _logger.info(f"✅ Page {page_id} subscribed to webhooks.")
            else:
                _logger.error(f"❌ Failed to subscribe page {page_id}: {sub_response}")

        return Response("✅ Connected successfully! Access token received.", status=200)

    @http.route('/acrux_webhook/whatsapp_connector/<string:connector_uuid>',
                auth='public', type='http', csrf=False, methods=['GET', 'POST'])
    def acrux_webhook(self, connector_uuid, **kwargs):
        ''' WhatsApp webhook endpoint for receiving messages and events '''
        try:
            _logger.info("🔔 WhatsApp webhook called for connector: %s", connector_uuid)

            # Handle GET request for webhook verification
            if request.httprequest.method == "GET":
                verify_token = request.httprequest.args.get("verify_token")
                challenge = request.httprequest.args.get("challenge")
                if verify_token and challenge:
                    return request.make_response(challenge, [('Content-Type', 'text/plain')])
                else:
                    return request.make_response("Webhook verification failed", [('Content-Type', 'text/plain')], 403)

            # Handle POST request for actual webhook data
            elif request.httprequest.method == "POST":
                _logger.info("📨 POST request received")

                # Get JSON data from request
                try:
                    if hasattr(request.httprequest, 'json') and request.httprequest.json:
                        data = request.httprequest.json
                    else:
                        raw_data = request.httprequest.data
                        if raw_data:
                            data = json.loads(raw_data.decode('utf-8'))
                        else:
                            data = {}
                    _logger.info("📨 Webhook data received: %s", json.dumps(data, indent=2))
                except Exception as e:
                    _logger.error("❌ Failed to parse webhook JSON data: %s", str(e))
                    data = {}

                # Find connector
                try:
                    Connector = request.env['acrux.chat.connector'].sudo()
                    connector = Connector.search([('uuid', '=', connector_uuid)], limit=1)
                    if connector:
                        _logger.info("✅ Found connector: %s (ID: %s)", connector.name, connector.id)
                    else:
                        # 🔍 Debug: Show available connectors when UUID not found
                        all_connectors = Connector.search([])
                        _logger.error("❌ Connector not found for UUID: %s", connector_uuid)
                        _logger.info("🔍 Available connectors in database:")
                        for conn in all_connectors:
                            _logger.info("   - ID: %s, Name: %s, Type: %s, UUID: %s, Active: %s",
                                       conn.id, conn.name, conn.connector_type, conn.uuid, conn.active)

                        # Print to terminal as well
                        print(f"\n{'='*80}")
                        print(f"❌ CONNECTOR NOT FOUND")
                        print(f"{'='*80}")
                        print(f"🔍 Looking for UUID: {connector_uuid}")
                        print(f"📋 Available connectors in database:")
                        for conn in all_connectors:
                            status = "✅ Active" if conn.active else "❌ Inactive"
                            print(f"   - ID: {conn.id}")
                            print(f"     Name: {conn.name}")
                            print(f"     Type: {conn.connector_type}")
                            print(f"     UUID: {conn.uuid}")
                            print(f"     Status: {status}")
                            print(f"     Created: {conn.create_date}")
                            print(f"     ---")
                        print(f"{'='*80}\n")
                        connector = None
                except Exception as e:
                    _logger.error("❌ Error finding connector: %s", str(e))
                    connector = None

                # Simple message processing
                try:
                    messages = data.get('messages', [])
                    _logger.info("📊 Found %d messages to process", len(messages))

                    if messages and connector:
                        WorkQueue = request.env['acrux.chat.work.queue'].with_user(SUPERUSER_ID).sudo()
                        for i, message in enumerate(messages):
                            try:
                                # 🔥 Print incoming WhatsApp message to terminal before processing
                                print(f"\n{'='*60}")
                                print(f"📱 INCOMING WHATSAPP MESSAGE #{i+1}")
                                print(f"{'='*60}")
                                print(f"📞 From: {message.get('from', 'Unknown')}")
                                print(f"📝 Text: {message.get('text', {}).get('body', 'No text content')}")
                                print(f"📋 Type: {message.get('type', 'Unknown')}")
                                print(f"🕐 Timestamp: {message.get('timestamp', 'No timestamp')}")
                                print(f"🆔 Message ID: {message.get('id', 'No ID')}")
                                print(f"🔗 Connector: {connector.name} (UUID: {connector.uuid})")
                                print(f"📄 Full Message Data:")
                                print(json.dumps(message, indent=2, ensure_ascii=False))
                                print(f"{'='*60}\n")

                                # Log to Odoo logger as well
                                _logger.info("📱 INCOMING MSG #%d: From=%s, Text=%s, Type=%s",
                                           i+1,
                                           message.get('from', 'Unknown'),
                                           message.get('text', {}).get('body', 'No text')[:100],
                                           message.get('type', 'Unknown'))

                                WorkQueue.create({
                                    'ttype': 'in_message',
                                    'connector_id': connector.id,
                                    'data': json.dumps(message)
                                })
                                _logger.info("✅ Message %d queued successfully", i+1)
                            except Exception as e:
                                _logger.error("❌ Failed to queue message %d: %s", i+1, str(e))
                except Exception as e:
                    _logger.error("❌ Message processing error: %s", str(e))

                # Process work queue if we have a connector
                if connector:
                    try:
                        WorkQueue = request.env['acrux.chat.work.queue'].with_user(SUPERUSER_ID).sudo()
                        WorkQueue._cron_process_queue([connector.id])
                        _logger.info("✅ Work queue processed for connector: %s", connector_uuid)
                    except Exception as e:
                        _logger.error("❌ Work queue processing error: %s", str(e))

                return request.make_response(
                    json.dumps({"status": "ok", "message": "Webhook processed successfully"}),
                    [('Content-Type', 'application/json')]
                )

        except Exception as e:
            _logger.error('❌ Webhook error for connector %s: %s', connector_uuid, str(e), exc_info=True)
            return request.make_response("Internal server error", [('Content-Type', 'text/plain')], 500)

    @http.route(['/web/chatresource/<int:id>/<string:access_token>',
                 '/web/static/chatresource/<string:model>/<string:id>/<string:field>'],
                type='http', auth='public', sitemap=False)
    def acrux_web_content(self, id=None, model=None, field=None, access_token=None):
        '''
        /web/chatresource/...        -> for attachment
        /web/static/chatresource/... -> for product image
        :param field: field (binary image, PNG or JPG) name in model. Only support 'image'.
        '''

        IrBinary = request.env['ir.binary'].sudo()
        try:
            if id and access_token and not model and not field:
                record = IrBinary._find_record(res_id=int(id), access_token=access_token)
                stream = IrBinary._get_stream_from(record)
            else:
                if not id or not field.startswith('image') or model not in acrux_allowed_models():
                    return Response(status=404)

                id, sep, unique = id.partition('_')
                record = IrBinary._find_record(res_model=model, res_id=int(id))
                stream = IrBinary._get_image_stream_from(record, field_name=field,
                                                         placeholder='web/static/img/XXXXX.png')
        except Exception:
            return Response(status=404)

        response = stream.get_response()
        return response


class Binary(http.Controller):

    @http.route('/web/binary/upload_attachment_chat', methods=['POST'], type='http', auth='user')
    def mail_attachment_upload(self, ufile, is_pending=False, connector_type=None, **kwargs):
        ''' Source: web.controllers.discuss.DiscussController.upload_attachment '''
        if ufile and ufile.mimetype:
            if (connector_type == 'instagram'):
                ufile = self.check_instagram_file(ufile)
            elif (connector_type == 'wechat'):
                ufile = self.check_wechat_file(ufile)
        try:
            limit = int(request.env['ir.config_parameter'].sudo().get_param('acrux_max_weight_kb') or '0')
            Attach = request.env['ir.attachment']
            datas = ufile.read()
            if len(datas) > limit * 1024:
                raise UserError(_('Too big, max. %s (%s)') % ('%sMb' % int(limit / 1000), ufile.filename))
            vals = {
                'name': ufile.filename,
                'raw': datas,
                'res_id': 0,
                'res_model': 'acrux.chat.message',
                'delete_old': True,
                'public': True
            }
            if is_pending and is_pending != 'false':
                # Add this point, the message related to the uploaded file does
                # not exist yet, so we use those placeholder values instead.
                vals.update({
                    'res_id': 0,
                    'res_model': 'acrux.chat.message',
                })
            vals['access_token'] = Attach._generate_access_token()
            attachment = Attach.create(vals)
            if ufile.mimetype:
                attachment.mimetype = ufile.mimetype
            attachment._post_add_create()
            attachmentData = {
                'filename': ufile.filename,
                'id': attachment.id,
                'mimetype': attachment.mimetype,
                'name': attachment.name,
                'size': attachment.file_size,
                'isAcrux': True,
                'res_model': vals['res_model']
            }
            if attachment.access_token:
                attachmentData['accessToken'] = attachment.access_token
        except UserError as e:
            attachmentData = {'error': e.args[0], 'filename': ufile.filename}
            _logger.exception("Fail to upload attachment %s" % ufile.filename)
        except Exception:
            attachmentData = {'error': _("Something horrible happened"), 'filename': ufile.filename}
            _logger.exception("Fail to upload attachment %s" % ufile.filename)
        return request.make_response(
            data=json.dumps(attachmentData),
            headers=[('Content-Type', 'application/json')]
        )

    def check_instagram_file(self, ufile):
        file_type = ufile.mimetype.split('/')[0]
        if (not pydub or file_type not in ['audio', 'video'] or
                ufile.mimetype in INSTAGRAM_AUDIO_FORMAT_ALLOWED or
                ufile.mimetype in INSTAGRAM_VIDEO_FORMAT_ALLOWED):
            return ufile
        data = ufile.read()
        try:
            if file_type == 'audio':
                output_io = self.convert_audio(data)
            else:
                output_io = self.convert_video_to_mp4(data, ufile.filename)
            ufile = FileStorage(stream=output_io, filename=f'{file_type}.mp4', content_type=f'{file_type}/mp4')
        except Exception as e:
            _logger.error(e)
            ufile = FileStorage(stream=BytesIO(data), filename=ufile.filename, content_type=ufile.mimetype)
        return ufile

    def check_wechat_file(self, ufile):
        file_type = ufile.mimetype.split('/')[0]
        if (not pydub or file_type not in ['audio', 'video'] or
                ufile.mimetype in WECHAT_AUDIO_FORMAT_ALLOWED or
                ufile.mimetype in WECHAT_VIDEO_FORMAT_ALLOWED):
            return ufile
        data = ufile.read()
        try:
            if file_type == 'audio':
                output_io = self.convert_audio(data, audio_format='mp3')
                ufile = FileStorage(stream=output_io, filename=f'{file_type}.mp3', content_type=f'{file_type}/mp3')
            else:
                output_io = self.convert_video_to_mp4(data, ufile.filename)
                ufile = FileStorage(stream=output_io, filename=f'{file_type}.mp4', content_type=f'{file_type}/mp4')
        except Exception as e:
            _logger.error(e)
            ufile = FileStorage(stream=BytesIO(data), filename=ufile.filename, content_type=ufile.mimetype)
        return ufile

    def convert_audio(self, data, audio_format='mp4'):
        file_like = BytesIO(data)
        audio = pydub.AudioSegment.from_file(file_like)
        output_io = BytesIO()
        audio.export(output_io, format=audio_format)
        return output_io

    def convert_video_to_mp4(self, data, filename):
        output_io = BytesIO(data)
        encoder = pydub.utils.get_encoder_name()
        if encoder:
            in_file = NamedTemporaryFile(mode='wb', suffix='.' + filename.split('.')[-1], delete=False)
            in_file.write(data)
            in_file.seek(0)
            output = NamedTemporaryFile(mode='w+b', suffix='.mp4', delete=False)
            conversion_command = [
                encoder, '-y', '-i', in_file.name,
                '-acodec', 'aac',
                '-vcodec', 'libx264',
                '-f', 'mp4', output.name
            ]
            with open(os.devnull, 'rb') as devnull:
                p = subprocess.Popen(conversion_command, stdin=devnull, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                _p_out, p_err = p.communicate()
            if p.returncode != 0:
                in_file.close()
                output.close()
                raise Exception(p_err.decode(errors='ignore'))
            output.seek(0)
            output_io = BytesIO(output.read())
            output.close()
            in_file.close()
        return output_io


class UsersController(http.Controller):

    @http.route('/users/create', auth='public', type='http', csrf=False, methods=['POST'])
    def create_user(self, **kwargs):
        try:
            # Read and decode the raw body
            raw_data = request.httprequest.data
            data = json.loads(raw_data.decode('utf-8'))

            name = data.get('name')
            login = data.get('login')
            password = data.get('password')
            email = data.get('email')
            role = data.get('role')
            companyId = data.get('companyId')

            if not all([name, login, password]):
                return {"error": "Missing required fields (name, login, password)."}

            existing_user = request.env['res.users'].sudo().with_context(active_test=False).search(
                [('login', '=', login)], limit=1)
            if existing_user:
                if not existing_user.active:
                    # Reactivate the user and update their info
                    existing_user.sudo().write({
                        'name': name,
                        'password': password,
                        'email': email,
                        'company_id': companyId,
                        'active': True
                    })

                    request.env.cr.commit()
                    return self.get_cookie(login, password)

                return Response(
                    json.dumps({"error": f"User with login '{login}' already exists."}),
                    status=409,
                    content_type='application/json'
                )

            user = request.env['res.users'].sudo().create({
                'company_id': companyId,
                'name': name,
                'login': login,
                'password': password,
                'email': email,
                'share': False
            })

            # Find the group named "From ChatRoom"
            group = request.env['res.groups'].sudo().search([('name', '=', role)], limit=1)
            if not group:
                return Response(json.dumps({"error": "Group 'From ChatRoom' not found"}), status=404,
                                content_type='application/json')

            # Add user to the group
            user.sudo().write({'groups_id': [(4, group.id)]})

            request.env.cr.commit()

            return self.get_cookie(login, password)

        except Exception as e:
            return Response(
                json.dumps({"error": str(e)}),
                status=500,
                content_type='application/json'
            )

    def get_cookie(self, login, password):
        if not all([login, password]):
            return request.redirect('/web/login?error=missing_params')

        base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')

        url = base_url + "/web/session/authenticate"
        headers = {"Content-Type": "application/json"}
        data = {
            "jsonrpc": "2.0",
            "params": {
                "db": request.env.registry.db_name,
                "login": login,
                "password": password
            }
        }

        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))
            res_data = response.json()

            if "result" in res_data and res_data["result"].get("uid"):
                # Set the session_id cookie manually so that /web knows the user is logged in
                session_id = response.cookies.get("session_id")
                return session_id
            else:
                return None
        except Exception as e:
            _logger.error(f'Error while getting cookie : {e}')
            return None

    @http.route('/users/direct_login', type='http', auth="public", methods=["GET"], csrf=False)
    def direct_login(self, session_id=None):
        if not session_id:
            return request.redirect("/web/login?error=missing_params")
        redirect_response = request.redirect("/web")
        redirect_response.set_cookie("cids", "1", path="/")
        redirect_response.set_cookie("session_id", session_id, path="/")
        return redirect_response

    @http.route('/users/deactivate', auth='public', type='http', csrf=False, methods=['POST'])
    def deactivate_user(self, **kwargs):
        try:
            # Read and decode the raw body
            raw_data = request.httprequest.data
            data = json.loads(raw_data.decode('utf-8'))

            login = data.get('login')

            if not login:
                return Response(
                    json.dumps({"error": "Missing required field: login"}),
                    status=400,
                    content_type='application/json'
                )

            # Search for the user by login
            user = request.env['res.users'].sudo().search([('login', '=', login)], limit=1)
            if not user:
                return Response(
                    json.dumps({"error": f"User with login '{login}' not found."}),
                    status=404,
                    content_type='application/json'
                )

            # Deactivate the user
            user.sudo().write({'active': False})
            request.env.cr.commit()

            return Response(
                json.dumps({"success": f"User '{login}' has been deactivated."}),
                status=200,
                content_type='application/json'
            )

        except Exception as e:
            return Response(
                json.dumps({"error": str(e)}),
                status=500,
                content_type='application/json'
            )


class AcruxTagController(http.Controller):

    @http.route('/acrux_chat_conversation_tags', type='json', auth='user')
    def get_tags(self):
        tags = request.env['acrux.chat.conversation.tag'].sudo().search([], order='name')
        return [{'id': tag.id, 'name': tag.name, 'color': tag.color} for tag in tags]
