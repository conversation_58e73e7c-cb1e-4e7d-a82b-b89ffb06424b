<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- AI Settings Form View -->
    <record model="ir.ui.view" id="view_whatsapp_connector_ai_settings_form">
        <field name="name">acrux.chat.ai.settings.form</field>
        <field name="model">acrux.chat.ai.settings</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="test_ai_connection" string="🔍 Test Connection" 
                            type="object" class="btn-primary"/>
                    <button name="toggle_ai_auto_reply" string="Toggle AI" 
                            type="object" class="btn-secondary"/>
                    <field name="connection_status" widget="status_bar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="ai_control" string="🤖 AI Control">
                            <field name="ai_auto_reply_enabled" widget="boolean_toggle"/>
                            <field name="connector_id" options="{'no_create': True}"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        
                        <group name="connection_info" string="🔗 Connection Status">
                            <field name="connection_status" readonly="1"/>
                            <field name="last_test_date" readonly="1"/>
                            <field name="ai_messages_sent" readonly="1"/>
                            <field name="success_rate" readonly="1" widget="percentage"/>
                        </group>
                    </group>
                    
                    <group string="⚙️ AI Service Configuration">
                        <field name="ai_service_token" password="True" placeholder="Optional API token"/>
                        <field name="ai_response_delay" widget="integer"/>
                    </group>
                    
                    <group string="🎛️ Behavior Settings">
                        <field name="ai_only_when_offline"/>
                        <field name="ai_fallback_enabled"/>
                        <field name="ai_fallback_message" 
                               invisible="ai_fallback_enabled == False" 
                               placeholder="Message to send when AI service is unavailable"/>
                    </group>
                    
                    <div class="oe_clear"/>
                    
                    <notebook>
                        <page string="📊 Statistics" name="statistics">
                            <group>
                                <group string="Usage Statistics">
                                    <field name="ai_messages_sent" readonly="1"/>
                                    <field name="success_rate" readonly="1" widget="percentage"/>
                                    <button name="action_reset_stats" string="Reset Statistics" 
                                            type="object" class="btn-warning btn-sm"/>
                                </group>
                                <group string="Recent Activity">
                                    <field name="last_test_date" readonly="1"/>
                                    <field name="connection_status" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="ℹ️ Help" name="help">
                            <div class="alert alert-info" role="alert">
                                <h4>🚀 Quick Setup Guide:</h4>
                                <ol>
                                    <li><strong>Start your Django AI service</strong> on port 8000</li>
                                    <li><strong>Test the connection</strong> using the "Test Connection" button</li>
                                    <li><strong>Enable AI Auto-Reply</strong> with the toggle switch</li>
                                    <li><strong>Configure behavior settings</strong> according to your needs</li>
                                </ol>
                                
                                <h4>🔧 Configuration Tips:</h4>
                                <ul>
                                    <li><strong>Response Delay:</strong> 2-3 seconds provides natural conversation flow</li>
                                    <li><strong>Only When Offline:</strong> Prevents AI from interrupting human agents</li>
                                    <li><strong>Fallback Message:</strong> Ensures customers always get a response</li>
                                </ul>
                                
                                <h4>📡 Django AI Service:</h4>
                                <p>Your AI service should be running at: <code>http://sales-agent-service.chatroom.svc.cluster.local:81/sales-agent/v1/chat/</code></p>
                                <p>Expected request format: <code>{"message": "text", "odoo_chat_id": "id", "metadata": {...}}</code></p>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- AI Settings List View -->
    <record model="ir.ui.view" id="view_whatsapp_connector_ai_settings_list">
        <field name="name">acrux.chat.ai.settings.list</field>
        <field name="model">acrux.chat.ai.settings</field>
        <field name="arch" type="xml">
            <list>
                <field name="ai_auto_reply_enabled" widget="boolean_toggle"/>
                <field name="display_name"/>
                <field name="connector_id"/>
                <field name="connection_status" 
                       decoration-success="connection_status == 'connected'"
                       decoration-danger="connection_status == 'failed'"
                       decoration-muted="connection_status == 'not_tested'"/>
                <field name="ai_messages_sent"/>
                <field name="success_rate" widget="percentage"/>
                <field name="last_test_date"/>
                <button name="test_ai_connection" string="Test" type="object" 
                        class="btn-primary btn-sm" icon="fa-refresh"/>
                <button name="toggle_ai_auto_reply" string="Toggle" type="object" 
                        class="btn-secondary btn-sm" icon="fa-power-off"/>
            </list>
        </field>
    </record>

    <!-- AI Settings Search View -->
    <record id="view_whatsapp_connector_ai_settings_search" model="ir.ui.view">
        <field name="name">acrux.chat.ai.settings.search</field>
        <field name="model">acrux.chat.ai.settings</field>
        <field name="arch" type="xml">
            <search string="AI Settings">
                <field name="display_name"/>
                <field name="connector_id"/>
                <field name="ai_service_url"/>
                
                <filter string="AI Enabled" name="ai_enabled" 
                        domain="[('ai_auto_reply_enabled', '=', True)]"/>
                <filter string="AI Disabled" name="ai_disabled" 
                        domain="[('ai_auto_reply_enabled', '=', False)]"/>
                <separator/>
                <filter string="Connected" name="connected" 
                        domain="[('connection_status', '=', 'connected')]"/>
                <filter string="Connection Failed" name="failed" 
                        domain="[('connection_status', '=', 'failed')]"/>
                <filter string="Not Tested" name="not_tested" 
                        domain="[('connection_status', '=', 'not_tested')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Connector" name="group_connector" 
                            context="{'group_by': 'connector_id'}"/>
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'ai_auto_reply_enabled'}"/>
                    <filter string="Connection" name="group_connection" 
                            context="{'group_by': 'connection_status'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- AI Settings Action -->
    <record model="ir.actions.act_window" id="view_whatsapp_connector_ai_settings_action">
        <field name="name">🤖 AI Chatbot Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">acrux.chat.ai.settings</field>
        <field name="view_mode">list,form</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                🤖 Configure your AI Chatbot!
            </p>
            <p>
                Set up automatic AI responses for your WhatsApp conversations.<br/>
                Create your first AI configuration to get started.
            </p>
        </field>
    </record>
</odoo>
