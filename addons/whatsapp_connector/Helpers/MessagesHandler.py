import logging
from odoo.http import request, Response
import json
from odoo import http, _, SUPERUSER_ID

_logger = logging.getLogger(__name__)


class MessagesHandler():

    def handle_meta_messages(self, payload):
        '''
        Keeping "Account ID" secret.
        '''
        try:
            if not payload:
                return Response(status=403)  # Forbidden

            WorkQueue = request.env['acrux.chat.work.queue'].with_user(SUPERUSER_ID).sudo()

            if 'entry' in payload and isinstance(payload['entry'], list):
                for entry in payload['entry']:
                    if 'messaging' in entry and isinstance(entry['messaging'], list):
                        for mess in entry['messaging']:
                            Connectors = request.env['acrux.chat.connector'].sudo()
                            connector = Connectors.search([
                                '|',
                                ('uuid', '=', mess['recipient']['id']),
                                ('uuid', '=', mess['sender']['id'])
                            ], limit=1)
                            WorkQueue.create({
                                'ttype': 'in_message',
                                'connector_id': connector.id,
                                'data': json.dumps(mess)
                            })


        except Exception:
            request.env.cr.rollback()
            _logger.error('Error', exc_info=True)
            return Response(status=500)  # Internal Server Error

    def handle_whatsapp_messages(self, connector, payload):
        _logger.info("📨 Processing WhatsApp webhook for connector %s (ID: %s)", connector.name, connector.id)
        _logger.info("📋 Payload structure: %s", json.dumps(payload, indent=2))

        # Add debug logging for troubleshooting
        _logger.info("🔧 DEBUG: Connector UUID: %s, Type: %s", connector.uuid, connector.connector_type)

        # Whapi.cloud webhook structure
        updates = payload.get('updates', [])
        events = payload.get('events', [])
        messages = payload.get('messages', [])
        statuses = payload.get('statuses', [])
        presences = payload.get('presences', [])

        # Log what we received
        _logger.info("📊 Received: %d updates, %d events, %d messages, %d statuses, %d presences",
                    len(updates), len(events), len(messages), len(statuses), len(presences))

        if not any([updates, events, messages, statuses, presences]):
            _logger.warning("⚠️ No processable data found in payload")
            return {'status': 'no_data', 'message': 'No processable data found'}

        WorkQueue = request.env['acrux.chat.work.queue'].with_user(SUPERUSER_ID).sudo()

        # Process updates (contact updates, status changes, etc.)
        for i, contact in enumerate(updates):
            _logger.info("🔄 Processing update %d/%d: %s", i+1, len(updates), json.dumps(contact))
            try:
                WorkQueue.create({
                    'ttype': 'in_update',
                    'connector_id': connector.id,
                    'data': json.dumps(contact)
                })
                _logger.info("✅ Update %d queued successfully", i+1)
            except Exception as e:
                _logger.error("❌ Failed to queue update %d: %s", i+1, str(e))

        # Process events (delivery receipts, read receipts, etc.)
        for i, event in enumerate(events):
            _logger.info("📅 Processing event %d/%d: %s", i+1, len(events), json.dumps(event))
            try:
                # Determine event type for proper processing
                event_type = event.get('type', 'unknown')
                if event_type in ['typing', 'recording', 'paused']:
                    ttype = 'in_typing'
                elif event_type in ['delivered', 'read', 'failed', 'sent']:
                    ttype = 'in_status'
                else:
                    ttype = 'in_event'

                WorkQueue.create({
                    'ttype': ttype,
                    'connector_id': connector.id,
                    'data': json.dumps(event)
                })
                _logger.info("✅ Event %d queued successfully as %s", i+1, ttype)
            except Exception as e:
                _logger.error("❌ Failed to queue event %d: %s", i+1, str(e))

        # Process messages (incoming messages)
        for i, mess in enumerate(messages):
            _logger.info("💬 Processing message %d/%d: %s", i+1, len(messages), json.dumps(mess))
            try:
                # 🔥 Print incoming WhatsApp message to terminal (Enhanced Handler)
                print(f"\n{'='*70}")
                print(f"📱 ENHANCED HANDLER - INCOMING MESSAGE #{i+1}")
                print(f"{'='*70}")
                print(f"📞 From: {mess.get('from', 'Unknown')}")
                print(f"📝 Text: {mess.get('text', {}).get('body', 'No text content')}")
                print(f"📋 Type: {mess.get('type', 'Unknown')}")
                print(f"🕐 Timestamp: {mess.get('timestamp', 'No timestamp')}")
                print(f"🆔 Message ID: {mess.get('id', 'No ID')}")
                print(f"🔗 Connector: {connector.name} (ID: {connector.id})")
                print(f"📄 Full Message Data:")
                print(json.dumps(mess, indent=2, ensure_ascii=False))
                print(f"{'='*70}\n")

                WorkQueue.create({
                    'ttype': 'in_message',
                    'connector_id': connector.id,
                    'data': json.dumps(mess)
                })
                _logger.info("✅ Message %d queued successfully", i+1)
            except Exception as e:
                _logger.error("❌ Failed to queue message %d: %s", i+1, str(e))

        # Process statuses (message delivery/read receipts)
        for i, status in enumerate(statuses):
            _logger.info("📋 Processing status %d/%d: %s", i+1, len(statuses), json.dumps(status))
            try:
                WorkQueue.create({
                    'ttype': 'in_status',
                    'connector_id': connector.id,
                    'data': json.dumps(status)
                })
                _logger.info("✅ Status %d queued successfully", i+1)
            except Exception as e:
                _logger.error("❌ Failed to queue status %d: %s", i+1, str(e))

        # Process presences (online/offline status, typing indicators)
        for i, presence in enumerate(presences):
            _logger.info("👤 Processing presence %d/%d: %s", i+1, len(presences), json.dumps(presence))
            try:
                # Determine presence type
                presence_type = presence.get('type', 'unknown')
                if presence_type in ['typing', 'recording', 'paused']:
                    ttype = 'in_typing'
                else:
                    ttype = 'in_presence'

                WorkQueue.create({
                    'ttype': ttype,
                    'connector_id': connector.id,
                    'data': json.dumps(presence)
                })
                _logger.info("✅ Presence %d queued successfully as %s", i+1, ttype)
            except Exception as e:
                _logger.error("❌ Failed to queue presence %d: %s", i+1, str(e))

        total_processed = len(updates) + len(events) + len(messages) + len(statuses) + len(presences)
        _logger.info("🎉 WhatsApp webhook processing completed: %d items processed for connector %s",
                    total_processed, connector.name)

        return {
            'status': 'ok',
            'processed': {
                'updates': len(updates),
                'events': len(events),
                'messages': len(messages),
                'statuses': len(statuses),
                'presences': len(presences),
                'total': total_processed
            }
        }


