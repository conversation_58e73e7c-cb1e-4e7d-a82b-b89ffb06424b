/* AI Settings Styles for WhatsApp Connector */

.ai-settings-container {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 10px 0;
}

.ai-toggle-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e3e6f0;
}

.ai-toggle-label {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.ai-toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.ai-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ai-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.ai-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .ai-toggle-slider {
    background-color: #4CAF50;
}

input:checked + .ai-toggle-slider:before {
    transform: translateX(26px);
}

.ai-connection-status {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e3e6f0;
}

.ai-connection-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.ai-connection-indicator.connected {
    background-color: #4CAF50;
}

.ai-connection-indicator.disconnected {
    background-color: #f44336;
}

.ai-connection-indicator.testing {
    background-color: #ff9800;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.ai-connection-text {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.ai-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.ai-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.ai-btn-primary {
    background-color: #007bff;
    color: white;
}

.ai-btn-primary:hover {
    background-color: #0056b3;
}

.ai-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.ai-btn-secondary:hover {
    background-color: #545b62;
}

.ai-help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 10px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}
