/* AI Settings Tab Styles */
.o_ai_settings_tab {
    padding: 15px;
    background: #f8f9fa;
    height: 100%;
    overflow-y: auto;
}

.ai-control-panel {
    max-width: 400px;
    margin: 0 auto;
}

.ai-status-header {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ai-status-badge .badge {
    font-size: 12px;
    padding: 6px 12px;
}

.ai-quick-controls .card,
.ai-connection-status .card,
.ai-stats .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.ai-quick-controls .card:hover,
.ai-connection-status .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #28a745;
    border-color: #28a745;
}

.custom-switch .custom-control-label::before {
    background-color: #dc3545;
    border-color: #dc3545;
}

.ai-connection-status .text-success {
    color: #28a745 !important;
}

.ai-connection-status .text-danger {
    color: #dc3545 !important;
}

.ai-connection-status .text-muted {
    color: #6c757d !important;
}

.ai-settings-link .btn {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    padding: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.ai-settings-link .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.ai-stats .stat-item {
    padding: 10px 0;
}

.ai-stats .stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
}

.ai-stats .stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ai-help-text .alert {
    border: none;
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-left: 4px solid #2196f3;
}

/* AI Message Indicators in Chat */
.ai-message-indicator {
    display: inline-block;
    background: #007bff;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
    font-weight: 500;
}

.ai-message-indicator::before {
    content: "🤖";
    margin-right: 3px;
}

/* Loading states */
.ai-loading {
    opacity: 0.6;
    pointer-events: none;
}

.ai-loading .btn::after {
    content: "";
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .o_ai_settings_tab {
        padding: 10px;
    }
    
    .ai-control-panel {
        max-width: 100%;
    }
    
    .ai-status-header {
        padding: 12px;
    }
    
    .ai-stats .row {
        margin: 0;
    }
    
    .ai-stats .col-6 {
        padding: 5px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .o_ai_settings_tab {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .ai-status-header,
    .ai-quick-controls .card,
    .ai-connection-status .card,
    .ai-stats .card {
        background: #34495e;
        color: #ecf0f1;
    }
    
    .ai-help-text .alert {
        background: linear-gradient(135deg, #34495e, #2c3e50);
        color: #ecf0f1;
    }
}
