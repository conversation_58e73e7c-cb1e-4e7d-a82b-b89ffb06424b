<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="chatroom.TabsContainer" owl="1">
        <NotebookChat className="'o_TabsContainer ' + props.className"
            onPageUpdate="tabId => this.env.chatBus.trigger('selectTab', tabId)"
            defaultPage="props.tabSelected"
            orientation="props.user.tabOrientation">
            <t t-set-slot="tab_default_answer" isVisible="true"
                name="'tab_default_answer'" icon="'fa fa-bolt'"
                id="'tab_default_answer'" title="titles.tab_default_answer">
                <div class="o_table_default">
                    <t t-foreach="defaultAnswers" t-as="answer" t-key="answer.id">
                        <DefaultAnswer defaultAnswer="answer"
                            selectedConversation="props.selectedConversation" />
                    </t>
                </div>
            </t>
            <t t-set-slot="tab_conv_info" isVisible="true"
                name="'tab_conv_info'" icon="'fa fa-info-circle'"
                id="'tab_conv_info'" title="titles.tab_conv_info">
                <div class="o_InfoForm">
                    <t t-set="tabComp" t-value="'ConversationForm'" />
                    <t t-call="chatroom.TabWithMyConversation" />
                </div>
            </t>
            <t t-set-slot="tab_partner" isVisible="true"
                name="'tab_partner'" icon="'fa fa-vcard-o'"
                id="'tab_partner'" title="titles.tab_partner">
                <div class="o_PartnerForm">
                    <t t-set="tabComp" t-value="'PartnerForm'" />
                    <t t-call="chatroom.TabWithConversation" />
                </div>
            </t>
            <t t-set-slot="tab_ai_settings" isVisible="true"
                name="'tab_ai_settings'" icon="'fa fa-magic'"
                id="'tab_ai_settings'" title="titles.tab_ai_settings">
                <div class="o_ai_settings_tab">
                    <div class="ai-control-panel">

                        <!-- Quick AI Toggle -->
                        <div class="ai-quick-controls mb-3">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Auto-Reply</strong>
                                            <br/>
                                            <small class="text-muted">Enable AI responses for incoming messages</small>
                                        </div>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox"
                                                   class="custom-control-input ai-toggle-switch"
                                                   id="aiToggleSwitch"
                                                   t-att-checked="aiSettings and aiSettings.ai_auto_reply_enabled"/>
                                            <label class="custom-control-label" for="aiToggleSwitch"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Connection Status -->
                        <div class="ai-connection-status mb-3">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Connection Status</strong>
                                            <br/>
                                            <small t-if="aiSettings">
                                                <span t-if="aiSettings.connection_status === 'connected'"
                                                      class="text-success">
                                                    <i class="fa fa-check-circle"></i> Connected to AI service
                                                </span>
                                                <span t-elif="aiSettings.connection_status === 'failed'"
                                                      class="text-danger">
                                                    <i class="fa fa-exclamation-triangle"></i> Connection failed
                                                </span>
                                                <span t-else="" class="text-muted">
                                                    <i class="fa fa-question-circle"></i> Not tested
                                                </span>
                                            </small>
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm"
                                                data-action="test-ai-connection">
                                            <i class="fa fa-refresh"></i> Test
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Statistics (if available) -->
                        <div t-if="aiSettings and aiSettings.ai_messages_sent > 0" class="ai-stats mt-3">
                            <div class="card">
                                <div class="card-body p-3">
                                    <h6 class="card-title">
                                        <i class="fa fa-chart-bar text-info"></i> Statistics
                                    </h6>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-number" t-esc="aiSettings.ai_messages_sent"/>
                                                <div class="stat-label">Messages</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="stat-item">
                                                <div class="stat-number" t-esc="Math.round(aiSettings.success_rate)"/>%
                                                <div class="stat-label">Success Rate</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Knowledge Base Admin -->
                        <div class="ai-knowledge-base mt-3">
                            <div class="card">
                                <div class="card-body p-3">
                                    <h6 class="card-title">
                                        <i class="fa fa-wrench text-warning"></i> Knowledge Base Admin
                                    </h6>
                                    <div class="knowledge-actions d-flex flex-column align-items-center">
                                        <button class="btn btn-outline-success btn-sm mb-2" data-action="kb-add-entry">
                                            <i class="fa fa-plus"></i> Add new entry
                                        </button>
                                        <button class="btn btn-outline-info btn-sm mb-2" data-action="kb-update-entry">
                                            <i class="fa fa-edit"></i> Update existing
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm mb-2" data-action="kb-view-entries">
                                            <i class="fa fa-eye"></i> View entries
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm mb-2" data-action="kb-load-append">
                                            <i class="fa fa-plus-square"></i> Load with append mode from PDF (keep existing)
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm mb-2" data-action="kb-load-overwrite">
                                            <i class="fa fa-refresh"></i> Load with overwrite mode from PDF (replace all)
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reports Section -->
                        <div class="ai-reports mt-3">
                            <div class="card">
                                <div class="card-body p-3">
                                    <h6 class="card-title">
                                        <i class="fa fa-bar-chart text-primary"></i> Reports
                                    </h6>
                                    <div class="report-actions d-flex flex-column align-items-center">
                                        <button class="btn btn-outline-info btn-sm mb-2" data-action="report-summary">
                                            <i class="fa fa-list-alt"></i> KPI Summary
                                        </button>
                                        <button class="btn btn-outline-success btn-sm mb-2" data-action="report-demos">
                                            <i class="fa fa-calendar-check-o"></i> Demo Bookings
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm mb-2" data-action="report-abandoned">
                                            <i class="fa fa-user-times"></i> Abandoned Customers
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm mb-2" data-action="report-customers">
                                            <i class="fa fa-users"></i> All Customers
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </t>
            <!-- <t t-set-slot="tab_ai_inteface" isVisible="true"
                name="'tab_ai_inteface'" iconHtml="comp.AiIntefaceForm.iconHtml"
                id="'tab_ai_inteface'" title="titles.tab_ai_inteface">
                <div class="o_AiIntefaceForm">
                    <t t-set="tabComp" t-value="'AiIntefaceForm'" />
                    <t t-call="chatroom.TabWithMyConversation" />
                </div>
            </t>
            <t t-set-slot="tab_product_grid" isVisible="true"
                name="'tab_product_grid'" icon="'fa fa-cubes'"
                id="'tab_product_grid'" title="titles.tab_product_grid">
                <ProductContainer selectedConversation="props.selectedConversation"/>
            </t>
            <t t-set-slot="tab_conv_panel" isVisible="true"
                name="'tab_conv_panel'" icon="'fa fa-clock-o'"
                id="'tab_conv_panel'" title="titles.tab_conv_panel">
                <div class="o_PanelForm">
                    <t t-set="tabComp" t-value="'ConversationPanelForm'" />
                    <t t-component="comp[tabComp]" t-props="this[`tab${tabComp}Props`]" />
                </div>
            </t>
            <t t-set-slot="tab_conv_kanban" isVisible="true"
                name="'tab_conv_kanban'" icon="'fa fa-th-large'"
                id="'tab_conv_kanban'" title="titles.tab_conv_kanban">
                <div class="o_KanbanForm">
                    <t t-set="tabComp" t-value="'ConversationKanban'" />
                    <t t-component="comp[tabComp]" t-props="this[`tab${tabComp}Props`]" />
                </div>
            </t> -->
        </NotebookChat>
    </t>

    <t t-name="chatroom.TabWithMyConversation" owl="1">
        <t t-if="props.selectedConversation">
            <t t-if="props.selectedConversation.isCurrent()">
                <t t-component="comp[tabComp]" t-props="this[`tab${tabComp}Props`]" />
            </t>
            <t t-else="">
                <t t-set="notYourConv" t-value="true" />
                <t t-call="chatroom.EmptyTab" />
            </t>
        </t>
        <t t-else="">
            <t t-call="chatroom.EmptyTab" />
        </t>
    </t>
  
    <t t-name="chatroom.TabWithConversation" owl="1">
        <t t-if="props.selectedConversation">
            <t t-component="comp[tabComp]" t-props="this[`tab${tabComp}Props`]" />
        </t>
        <t t-else="">
            <t t-call="chatroom.EmptyTab" />
        </t>
    </t>
  
    <t t-name="chatroom.EmptyTab" owl="1">
        <div class="o_EmptyTab">
            <span>
                <t t-if="message"> <t t-esc="message" /> </t>
                <t t-elif="notYourConv">You are not attending this conversation.</t>
                <t t-else="">You must select a conversation first.</t>
            </span>
        </div>
    </t>

</templates>