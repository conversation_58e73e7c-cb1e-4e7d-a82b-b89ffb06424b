# -*- coding: utf-8 -*-
import logging
import re
import time
from typing import List
from time import sleep

import requests
import json
import vobject
from psycopg2 import IntegrityError, errorcodes
from markupsafe import Markup
from odoo import models, fields, api, _
from odoo.osv import expression
from odoo.exceptions import ValidationError
from odoo.tools import formatLang
from odoo.tools.safe_eval import safe_eval
from datetime import datetime, date

from ..Helpers import WhapiHelper
from ..tools import DEFAULT_IMAGE_URL
from ..tools import get_image_url, get_image_from_url, get_binary_attach
from ..tools import date_timedelta, date2sure_write

_logger = logging.getLogger(__name__)

AVAILABLE_PRIORITIES = [
    ('0', 'Low'),
    ('1', 'Medium'),
    ('2', 'High'),
    ('3', 'Very High'),
]

FACEBOOK_GRAPH_API_URL = "https://graph.facebook.com"


class AcruxChatConversation(models.Model):
    _name = 'acrux.chat.conversation'
    _description = 'ChatRoom Conversation'
    _order = 'last_activity desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Name', required=True)
    number = fields.Char('Base number', required=True, index=True)
    number_format = fields.Char('Number', compute='_compute_number_format',
                                store=True, readonly=True)
    conv_type = fields.Selection([('normal', 'Normal'),
                                  ('private', 'Private'),
                                  ('group', 'Group')], readonly=True,
                                 string='Conversation Type', required=True, default='normal')
    image_128 = fields.Image('Avatar', max_width=128, max_height=128)
    image_url = fields.Char('Avatar Url', compute='_image_update', store=True,
                            default=DEFAULT_IMAGE_URL, required=True)
    connector_id = fields.Many2one('acrux.chat.connector', 'Connector', required=True,
                                   ondelete='cascade')
    res_partner_id = fields.Many2one('res.partner', 'Client', ondelete='set null')
    partner_info = fields.Char('Extra Info', compute='_compute_partner_info')
    status = fields.Selection([('new', 'New'),
                               ('current', 'Current'),
                               ('done', 'Done')], 'Status', required=True,
                              default='new', tracking=False)
    chat_message_ids = fields.One2many('acrux.chat.message', 'contact_id', 'Chat Messages')
    agent_id = fields.Many2one('res.users', 'Agent', ondelete='set null',
                               domain="[('company_id', 'in', [company_id, False]), ('is_chatroom_group','=',True)]")
    last_activity = fields.Datetime('Last activity', compute='_last_activity', required=True, store=True,
                                    default=fields.Datetime.now)
    last_sent = fields.Datetime('Last sent', compute='_last_activity', store=True,
                                help='Last message sent to the partner.')
    last_received = fields.Datetime('Last Received', compute='_last_activity', store=True,
                                    help='To prevent send message with extra fee.')
    last_received_first = fields.Datetime('First Unanswered', compute='_last_activity', store=True,
                                          help='First unanswered message.')
    unanswered = fields.Boolean('Unanswered', compute='_last_activity', store=True)
    company_id = fields.Many2one('res.company', related='connector_id.company_id', string='Company',
                                 store=True, readonly=True)
    team_id = fields.Many2one('crm.team', string='Team',
                              domain="[('company_id', 'in', [company_id, False])]",
                              ondelete='set null')
    border_color = fields.Char(related='connector_id.border_color', store=False)
    desk_notify = fields.Selection(related='connector_id.desk_notify', store=False)
    connector_type = fields.Selection(related='connector_id.connector_type', store=False)
    show_icon = fields.Boolean(related='connector_id.show_icon', store=False)
    allow_signing = fields.Boolean(related='connector_id.allow_signing', store=False)
    valid_number = fields.Selection([('yes', 'Yes'),
                                     ('no', 'No')], string='Valid', default=False, help='Exists in WhatsApp')
    tmp_agent_id = fields.Many2one('res.users', 'Assign to', ondelete='set null',
                                   domain="[('company_id', 'in', [company_id, False]), ('is_chatroom_group','=',True)]")
    is_waba_opt_in = fields.Boolean('Opt-in', default=True)
    sent_opt_in = fields.Boolean('Opt-in already requested', default=True)
    mute_opt_in = fields.Boolean()
    stage_id = fields.Many2one('acrux.chat.conversation.stage', string='Stage', index=True,
                               tracking=True, copy=False,
                               group_expand='_read_group_stage_ids')
    kanban_state = fields.Selection([('grey', 'No next activity planned'),
                                     ('red', 'Next activity late'),
                                     ('green', 'Next activity is planned')],
                                    string='Kanban State', compute='_compute_kanban_state')
    priority = fields.Selection(AVAILABLE_PRIORITIES, string='Priority',
                                index=True, default=AVAILABLE_PRIORITIES[0][0])
    color = fields.Integer('Color Index', default=0)
    tag_ids = fields.Many2many('acrux.chat.conversation.tag', string='Tags')
    note = fields.Text('Note', help='Internal note.')
    is_odoo_created = fields.Boolean('Is odoo created?', default=False)
    description = fields.Text('Description', help='Conversation description. '
                                                  'May be synchronized with external connector.')
    allowed_lang_ids = fields.Many2many(related='connector_id.allowed_lang_ids')
    oldes_activity_date = fields.Datetime('Oldest Activity', store=True,
                                          compute='_compute_oldest_activity')
    chat_id = fields.Char('Chat Id')

    draft_order_ids = fields.One2many(
        comodel_name='draft.order',
        inverse_name='conversation_id',
        string='Draft Orders'
    )

    isHappy = fields.Char('Is customer happy?')
    summary = fields.Text('Summary for the last 100 message in the conversation:')
    rating = fields.Integer('Overall rating:', default=10)

    # AI Auto-Reply Control per Conversation
    ai_auto_reply_enabled = fields.Boolean(
        'Enable AI Auto-Reply for this Conversation',
        default=False,
        help='Enable AI responses for this specific conversation. '
             'Note: Connector-level AI must also be enabled for AI to work.'
    )

    # hint:: create here to by many to one with draft order model

    def summarize(self):
        messages = self.env['acrux.chat.message'].search_read(
            domain=[('contact_id', '=', self.id)],
            fields=['contact_id', 'text', 'create_date', 'user_id'],
            offset=0,
            limit=100,
            order='create_date DESC'
        )
        structured_messages = []
        for msg in messages:
            create_date = msg['create_date']
            if isinstance(create_date, datetime):
                iso_timestamp = create_date.isoformat() + 'Z'
            else:
                try:
                    dt = datetime.strptime(create_date, '%Y-%m-%d %H:%M:%S')
                    iso_timestamp = dt.isoformat() + 'Z'
                except Exception:
                    iso_timestamp = str(create_date)
            structured_messages.append({
                'sender': msg['contact_id'][1] if msg['contact_id'] else 'Unknown',
                'receiver': msg['user_id'][1] if msg['user_id'] else 'Unknown',
                'timestamp': iso_timestamp,
                'text': msg['text'],
            })
        try:
            response = requests.post(
                'http://chatroom-ai.chatroom.svc.cluster.local:8180/ai/analyze',
                headers={'Content-Type': 'application/json'},
                data=json.dumps(structured_messages)
            )
            response.raise_for_status()
            result = response.json()
            _logger.info("yahoooooo here is the result: %s", result)
            self.summary = result['summary']
            self.isHappy = result['is_happy']
            self.rating = result['rating']
            orders_data = result.get('orders', [])
            for order in orders_data:
                self.env['draft.order'].create({
                    'customer_name': order.get('customer_name'),
                    'area': order.get('area'),
                    'country': order.get('country'),
                    'note': order.get('note'),
                    'total_cod': order.get('total_cod'),
                    'address': order.get('address'),
                    'sub_area': order.get('sub_area'),
                    'customer_mobile': order.get('customer_mobile'),
                    'conversation_id': self.id,  # Link to current conversation
                })
        except requests.exceptions.RequestException as e:
            _logger.error("Error calling AI analyze service: %s", e)
            self.summary = "Something went wrong, please try again."

    _sql_constraints = [
        ('unique_connector_chat', 'unique(connector_id, chat_id)',
         'A conversation with the same connector and chat ID already exists.')
    ]

    def is_private(self):
        return self.conv_type == 'private'

    def is_group(self):
        return self.conv_type == 'group'

    @api.constrains('status', 'agent_id')
    def _constrain_status(self):
        for r in self:
            if r.status == 'current' and not r.agent_id:
                raise ValidationError(_('Have to set agent to set conversation to "current"'))

    @api.constrains('number', 'connector_id', 'conv_type')
    def _constrain_number(self):
        for r in self.filtered(lambda conv: conv.connector_id and conv.number and
                                            not conv.is_private() and
                                            not conv.is_group()):
            r.connector_id.assert_id(r.number)

    @api.depends('number', 'connector_id', 'conv_type')
    def _compute_number_format(self):
        to_process = self.filtered(lambda conv: conv.connector_id and conv.number and
                                                not conv.is_private() and
                                                not conv.is_group())
        for rec in to_process:
            rec.number_format = rec.connector_id.format_id(rec.number)
        for rec in self - to_process:
            rec.number_format = rec.number

    @api.onchange('number', 'connector_id', 'conv_type')
    def _onchange_number(self):
        for r in self.filtered(lambda conv: conv.connector_id and conv.number and
                                            not conv.is_private() and
                                            not conv.is_group()):
            r.number = r.connector_id.clean_id(r.number)

    @api.onchange('res_partner_id')
    def onchange_res_partner_id(self):
        if self.res_partner_id and self.env.context.get('set_default'):
            self.name = self.res_partner_id.name
            number = self.res_partner_id.mobile or self.res_partner_id.phone
            if number and (not self.connector_id or
                           self.connector_id and not self.connector_id.is_facebook_or_instagram()):
                self.number = number

    @api.model_create_multi
    def create(self, vals_list):
        res = super(AcruxChatConversation, self).create(vals_list)
        for ret in res:
            if (self.env.context.get('is_from_wizard') or self.env.context.get('is_acrux_chat_room')) \
                    and not self.env.context.get('not_check_is_valid') \
                    and ret.connector_id.check_is_valid_active() and not ret.valid_number:
                ret.connector_id.check_is_valid_whatsapp_number(ret, raise_error=False)
                if ret.valid_number == 'no':
                    error = _('Number not exist in WhatsApp (%s)') % ret.number
                    raise ValidationError(error)
                elif not ret.valid_number and ret.connector_id.valid_restriction:
                    error = _('The number could not be verified (%s)') % ret.number
                    raise ValidationError(error)
            ret.update_conversation()
        return res

    def sync_latest_messages(self):
        if self.connector_id.connector_type == 'whapi':
            whapi_helper = WhapiHelper()
            whapi_messages = whapi_helper.get_message_list(self.connector_id, self.chat_id)
            if not whapi_messages:
                return
            whapi_message_ids = [msg['id'] for msg in whapi_messages]
            if whapi_message_ids is None:
                return
            existing_ids = self.get_existing_message_ids(whapi_message_ids)

            new_messages = [msg for msg in whapi_messages if msg['id'] not in existing_ids]

            for msg in new_messages:
                parse_data = self.parse_message_receive(self.connector_id, msg)
                if parse_data:
                    message = self.new_message(parse_data)
                    self.env.cr.commit()

    def get_existing_message_ids(self, message_ids):
        Message = self.env['acrux.chat.message']
        existing = Message.search([('msgid', 'in', message_ids)])
        return set(existing.mapped('msgid'))

    def action_check_is_valid(self):
        recs = dict()
        for rec in self:
            if rec.connector_id.connector_type == 'apichat.io' and rec.connector_id.auto_valid_number:
                if rec.connector_id.id not in recs:
                    recs[rec.connector_id.id] = {'conv_ids': self.env['acrux.chat.conversation'],
                                                 'conn_id': rec.connector_id}
                recs[rec.connector_id.id]['conv_ids'] |= rec
        for conn in recs.values():
            # raise if expired or reached
            conn['conn_id'].check_is_valid_whatsapp_number(conn['conv_ids'])

    def update_conversation(self):
        self.ensure_one()
        if self.env.context.get('not_download_profile_picture'):
            return
        if self.connector_id.connector_type == 'apichat.io':
            params = {'chatId': self.get_chat_id()}
            self._update_conversation(params, timeout=5)
        elif self.connector_id.is_facebook_or_instagram():
            params = {'chatId': self.number}
            self._update_conversation(params, timeout=None)

    def get_chat_id(self):
        self.ensure_one()
        out = self.number
        if self.connector_id.connector_type == 'apichat.io':
            if self.conv_type == 'normal':
                out = f'{self.number}@c.us'
            elif self.conv_type == 'private':
                out = f'{self.number}@l.us'
            elif self.conv_type == 'group':
                out = f'{self.number}@g.us'
        return out

    def _update_conversation(self, params, timeout):
        self.ensure_one()
        try:
            data = self.connector_id.ca_request('contact_get', params=params,
                                                timeout=timeout)
            name = data.get('name')
            if name:
                self.name = name.strip()
            image_url = data.get('image')
            if image_url and image_url.startswith('http'):
                raw = get_image_from_url(image_url)
                if raw:
                    self.image_128 = raw
        except Exception:
            pass

    def write(self, vals):
        if vals.get('status') and self.env.context.get('please_log_event'):
            event = {'new': 'to_new',
                     'done': 'to_done',
                     'current': 'to_curr'}
            self.event_create(event.get(vals['status']))
        return super(AcruxChatConversation, self).write(vals)

    def event_create(self, event, user_id=False, text=False):
        if not self.env.context.get('not_log_event'):
            if not user_id:
                user_id = self.env.user
            Message = self.env['acrux.chat.message']
            for rec in self:
                txt = text or dict(Message._fields['event'].selection).get(event)
                data = {'ttype': 'info',
                        'from_me': True,  # By convention
                        'contact_id': rec.id,
                        'event': event,
                        'user_id': user_id.id,
                        'text': '%s (%s)' % (txt, user_id.name)}
                Message.create(data)

    @api.depends('res_partner_id')
    def _compute_partner_info(self):
        for rec in self:
            info = ''
            if rec.res_partner_id:
                p_id = rec.res_partner_id
                info = '%s: %s' % (_('Email'), p_id.email or '-')
                phones = list(set([p_id.phone, p_id.mobile]))
                phones = ' | '.join([x for x in phones if x])
                info += '\n%s: %s' % (_('Phone'), phones or '-')
                locations = '. '.join([x for x in [p_id.city, p_id.country_id.name] if x])
                info += '\n%s: %s' % (_('Location'), locations or '-')
            rec.partner_info = info

    @api.depends('chat_message_ids')
    def _last_activity(self):
        for rec in self:
            if rec.chat_message_ids:
                rec.conversation_update_time(rec.chat_message_ids[0])

    @api.model
    def conversation_update_time(self, mess):
        if mess.active and not mess.ttype.startswith('info'):
            data = {}
            cont = mess.contact_id
            if mess.from_me:
                data.update({'last_sent': mess.date_message})
                if cont.last_received:
                    data.update({'last_received_first': False})
            else:
                # nº message
                data.update({'last_received': mess.date_message})
                # 1º message
                if not cont.last_received_first:
                    data.update({'last_received_first': mess.date_message})

            last_sent = data.get('last_sent')
            last_received = data.get('last_received')
            exist = last_sent or last_received
            if exist:
                last = max(last_sent or exist, last_received or exist)
            else:
                last = fields.Datetime.now()
            data.update({'last_activity': last})
            data.update({'unanswered': data.get('last_activity') == data.get('last_received')})
            cont.update(data)

    @api.depends('image_128', 'res_partner_id.image_128')
    def _image_update(self):
        for rec in self:
            if rec.image_128 and rec.write_date:
                rec.image_url = get_image_url(self, rec, rec.image_128)
            elif rec.res_partner_id.image_128:
                rec.image_url = get_image_url(self, rec.res_partner_id, rec.res_partner_id.image_128)
            else:
                rec.image_url = DEFAULT_IMAGE_URL

    @api.depends('name', 'number_format')
    def name_get(self):
        result = []
        full_name = self.env.context.get('full_name')
        for conv in self:
            if full_name:
                result.append((conv.id, _('To: %s (%s) | From: %s') %
                               (conv.name, conv.number_format, conv.connector_id.name)))
            else:
                result.append((conv.id, '%s (%s)' % (conv.name, conv.number_format)))
        return result

    @api.model
    def _name_search(self, name, domain=None, operator='ilike', limit=None, order=None):
        domain = domain or []
        if operator == 'ilike' and not (name or '').strip():
            args = []
        else:
            args = ['|', ('name', 'ilike', name), ('number', 'ilike', name)]
        return self._search(expression.AND([domain, args]), limit=limit)

    def get_to_done(self):
        self.ensure_one()
        return {'status': 'done',
                'agent_id': False}

    def get_to_current(self):
        self.ensure_one()
        return {'agent_id': self.env.user.id,
                'status': 'current'}

    def get_to_new(self):
        self.ensure_one()
        return {'status': 'new',
                'agent_id': False}

    def set_to_done(self):
        for r in self:
            r.event_create('to_done')
            r.write(r.get_to_done())

    def set_to_current(self):
        for r in self:
            r.event_create('to_curr')
            r.write(r.get_to_current())

    def set_to_new(self):
        for r in self:
            r.write(r.get_to_new())

    @api.model
    def new_message(self, data):
        '''
        Processes received message (WebHook).
        :return: objetc message_id
        '''

        Messages = self.env['acrux.chat.message']
        msg_id = data.get('msgid')

        # 🔥 Print message creation to terminal
        print(f"\n{'='*90}")
        print(f"💬 CREATING NEW MESSAGE IN CONVERSATION")
        print(f"{'='*90}")
        print(f"🆔 Message ID: {msg_id}")
        print(f"📞 From: {data.get('from_me', False) and 'Business' or data.get('number', 'Unknown')}")
        print(f"📝 Text: {data.get('text', 'No text content')}")
        print(f"📋 Type: {data.get('ttype', 'Unknown')}")
        print(f"🔗 Connector ID: {data.get('connector_id', 'Unknown')}")
        print(f"📄 Message Data:")
        print(json.dumps(data, indent=2, ensure_ascii=False, default=str))
        print(f"{'='*90}\n")

        time.sleep(1)
        existing_message = Messages.search([('msgid', '=', msg_id)], limit=1)
        if existing_message:
            _logger.info("Duplicate message detected (msgid: %s), skipping processing.", msg_id)
            print(f"⚠️ DUPLICATE MESSAGE DETECTED: {msg_id} - Skipping processing\n")
            return existing_message

        Connector = self.env['acrux.chat.connector']
        connector = Connector.browse(data['connector_id'])
        conversation = self.create_conversation_from_message_dict(data)

        if not conversation.id:
            return None

        message = Messages.create({
            'text': data['message'],
            'contact_id': conversation.id,
            'contact_name': data['name'],
            'contact_number': data['number'],
            'ttype': data['ttype'],
            'msgid': data['msgid'],
            'date_message': date2sure_write(data['time']),
            'from_me': data.get('from_me', False),
            'sent_date': data.get('time'),
        })

        message.post_create_from_json(data, connector)
        limit = conversation.decide_first_status()
        limit, send_bus = self.new_message_hook(connector, message, limit, data)
        
        # AI Auto Reply Integration - Trigger AI if conditions are met
        if not message.from_me and conversation._should_trigger_ai_reply(message):
            try:
                # 🔥 Print AI trigger to terminal
                print(f"\n{'='*100}")
                print(f"🤖 AI AUTO-REPLY TRIGGERED")
                print(f"{'='*100}")
                print(f"📞 Customer: {conversation.number}")
                print(f"💬 Customer Name: {conversation.name or 'Unknown'}")
                print(f"📝 Incoming Message: {message.text}")
                print(f"🔗 Conversation ID: {conversation.id}")
                print(f"🆔 Message ID: {message.id}")
                print(f"{'='*100}\n")

                # Add delay if configured
                ai_settings_model = conversation.env['acrux.chat.ai.settings']
                domain = [
                    '|',
                    ('connector_id', '=', conversation.connector_id.id),
                    ('connector_id', '=', False)
                ]
                ai_settings = ai_settings_model.search(domain, order='connector_id desc', limit=1)

                if ai_settings and ai_settings.ai_response_delay > 0:
                    print(f"⏰ AI Response Delay: {ai_settings.ai_response_delay} seconds")
                    time.sleep(ai_settings.ai_response_delay)

                # Call AI reply directly (no threading needed for simple calls)
                conversation._generate_and_send_ai_reply(message.text, message)
                _logger.info(f"AI auto-reply triggered for message: {message.text[:50]}...")
                
            except Exception as e:
                _logger.error(f"AI auto-reply error: {str(e)}")
                # Try fallback message
                try:
                    if ai_settings and ai_settings.ai_fallback_enabled and ai_settings.ai_fallback_message:
                        conversation.send_message({
                            'text': ai_settings.ai_fallback_message,
                            'ttype': 'text',
                            'from_me': True  # ✅ This marks it as a reply FROM the business
                        }, check_access=False)
                except:
                    pass  # If fallback also fails, continue gracefully
        
        if conversation.env.context.get('downl_later'):
            self.env.cr.commit()  # comment for savepoint in queue cron
            conversation.with_context(not_download_profile_picture=False).update_conversation()
        if send_bus:
            data_to_send = conversation.build_dict(limit)
            conversation._sendone(conversation.get_channel_to_many(), 'new_messages', data_to_send)
        return message

    @api.model
    def create_conversation_from_message_dict(self, data):
        conversation = self.env['acrux.chat.conversation']
        max_tries = 0
        please_set_to_new = False
        while max_tries < 3:
            max_tries += 1
            conversation = self.search([('conv_type', '=', data['conv_type']),
                                        ('chat_id', '=', data['chat_id']),
                                        ('connector_id', '=', data['connector_id'])])
            if conversation:
                conversation.name = data['chat_name'] or data['name']
                if conversation.valid_number != 'yes':
                    conversation.valid_number = 'yes'
                if not conversation.is_waba_opt_in:
                    conversation.is_waba_opt_in = True
            if not conversation:
                try:
                    vals = self.create_conversation_from_message_dict_vals(data)
                    conversation = self.with_context(not_download_profile_picture=True, downl_later=True).create(vals)
                    self.env.cr.commit()
                    please_set_to_new = True
                except IntegrityError as e:
                    if e.pgcode == errorcodes.UNIQUE_VIOLATION:
                        self.env.cr.rollback()
                        sleep(1)
                        continue
            if not conversation.res_partner_id and conversation.status in ['new', 'done']:
                partner_id = self.search_partner_from_number(conversation)
                if partner_id:
                    conversation.res_partner_id = partner_id[0]
            if please_set_to_new:
                conversation.set_to_new()
            break
        return conversation

    @api.model
    def create_conversation_from_message_dict_vals(self, data):
        return {
            'name': data.get('chat_name') or data.get('name'),
            'connector_id': data['connector_id'],
            'valid_number': 'yes',
            'chat_id': data['chat_id'],
            'is_waba_opt_in': True,
            'number': data['chat_id'].split('@')[0] if data['from_me'] else data['number'],
            'conv_type': data['conv_type'],
        }

    def decide_first_status(self):
        self.ensure_one()
        limit = 1
        if self.status == 'done':
            self.set_to_new()
            limit = 22
        elif self.status == 'current':
            if (self.connector_id.reassign_current_conversation and
                    not self.agent_id.chatroom_active()):
                self.with_context(force_reassign=True).set_to_new()
                limit = 22
            else:
                limit = 100
        else:
            limit = 1
        return limit

    def new_message_hook(self, connector, message_id, limit, data):
        if data.get('quote_msg_id'):
            AcruxChatMessages = self.env['acrux.chat.message']
            message_obj = AcruxChatMessages.search([
                ('contact_id', '=', message_id.contact_id.id),
                ('msgid', '=', data['quote_msg_id'])
            ], limit=1)

            quote_message_id = None

            if not message_obj:

                whapi_helper = WhapiHelper()
                message_details = whapi_helper.get_message_content(connector, data['quote_msg_id'])

                if message_details:
                    parse_data = self.parse_message_receive(connector, message_details)
                    if parse_data:
                        new_message = self.new_message(parse_data)
                        quote_message_id = new_message.id
            else:
                quote_message_id = message_obj.id

            # Link the current message to the quoted one
            message_id.write({'quote_id': quote_message_id})

        return limit, True

    def get_channel_to_many(self):
        for rec in self:
            return self.env.cr.dbname, self._name, rec.connector_id.company_id.id, rec.connector_id.id
        # self.ensure_one()

    def get_channel_to_one(self, user_id=None):
        self.ensure_one()
        if not user_id:
            user_id = self.agent_id
        # Add user at the end
        return self.env.cr.dbname, self._name, 'private', self.connector_id.company_id.id, user_id.id

    @api.model
    def parse_notification(self, datas):
        return datas

    def _sendmany(self, datas):
        _logger.info('_sendmany : Sending message %s to channel %s' % (datas, self.get_channel_to_many()))
        notifications = self.parse_notification(datas)
        if notifications:
            for notify in notifications:
                channel, notification_type, message = notify
                self.env['bus.bus']._sendone(channel, notification_type, message)

    def _sendone(self, channel, notification_type, message):
        _logger.info("_sendone : Sending message %s to channel %s" % (message, channel))
        self._sendmany([[channel, notification_type, message]])

    def update_conversation_bus(self):
        data_to_send = self.build_dict(limit=0)
        self._sendone(self.get_channel_to_many(), 'update_conversation', data_to_send)

    @api.model
    def new_message_event(self, connector_id, msgid, data):
        '''
        :todo pensar en js que hacer con esta notificacion
        '''
        Messages = self.env['acrux.chat.message']
        message_id = Messages.search([('connector_id', '=', connector_id.id),
                                      ('msgid', '=', msgid)], limit=1)
        if message_id:
            message_id.process_message_event(data)
            if not message_id.mute_notify:
                conv_id = message_id.contact_id
                data_to_send = conv_id.build_dict(limit=0)
                data_to_send[0]['messages'] = message_id.get_js_dict()
                if data['type'] == 'failed':
                    conv_id._sendone(conv_id.get_channel_to_many(), 'error_messages', data_to_send)
                else:
                    conv_id._sendone(conv_id.get_channel_to_many(), 'update_conversation', data_to_send)
        return message_id

    def get_product_caption(self, product_id):
        self.ensure_one()
        if not product_id:
            raise ValidationError('Product is required.')
        text = ''
        product_caption = (self.connector_id.product_caption or '').strip()
        if product_caption:
            def format_price(price):
                return formatLang(self.env, price, currency_obj=self.env.company.currency_id)

            local_dict = {
                'env': self.env,
                'format_price': format_price,
                'product_id': product_id,
                'conversation_id': self,
                'text': ''
            }
            safe_eval(product_caption, locals_dict=local_dict, mode='exec', nocopy=True)
            text = local_dict.get('text', '') or ''
        return (text or '').strip()

    def send_message(self, msg_data, check_access=True):
        self.ensure_one()
        if check_access:
            if self.status != 'current':
                raise ValidationError(_('You can\'t write in this conversation, please refresh the screen.'))
            if not self.env.user.has_group('whatsapp_connector.group_chatroom_admin'):
                if self.agent_id != self.env.user:
                    raise ValidationError(_('This conversation is no longer attended to by you.'))
        AcruxChatMessages = self.env['acrux.chat.message']
        js_id = msg_data['id'] if 'id' in msg_data and msg_data['id'] < 0 else None
        msg_data['contact_id'] = self.id
        msg_data = self.split_complex_message(msg_data)
        if msg_data.get('chat_list_id'):
            ListModel = self.env['acrux.chat.message.list']
            msg_data['chat_list_id'] = ListModel.browse(msg_data['chat_list_id']).copy({'active': False}).id

        message_obj = AcruxChatMessages.create(msg_data)
        message_obj.message_send()
        data_to_send = self.build_dict(limit=0)
        data_to_send[0]['messages'] = message_obj.get_js_dict()
        data_to_send[0]['messages'][0]['js_id'] = js_id
        self._sendone(self.get_channel_to_many(), 'update_conversation', data_to_send)
        return message_obj.get_js_dict()

    def send_message_product(self, prod_id):
        self.ensure_one()
        product_id = self.env['product.product'].browse(prod_id)
        attach = get_binary_attach(self.env, 'product.product', prod_id, 'image_512',
                                   fields_ret=['id'], product_id=product_id)
        if attach:
            msg_data = {
                'text': self.get_product_caption(product_id),
                'from_me': True,
                'is_product': True,
                'ttype': 'image',
                'res_model': 'ir.attachment',
                'res_id': attach.get('id'),
            }
        else:
            msg_data = {
                'text': self.get_product_caption(product_id) or product_id.display_name.strip(),
                'from_me': True,
                'ttype': 'text',
            }
        self.send_message_bus_release(msg_data, self.status)

    def split_complex_message(self, msg_data):
        if (self.connector_id.is_facebook_or_instagram() and
                msg_data['ttype'] in ('product', 'image', 'video', 'file', 'audio')):

            def create_text_message(msg_origin, caption):
                msg_2nd = msg_origin.copy()
                msg_2nd.update({'ttype': 'text', 'text': caption, 'res_model': False, 'res_id': False})
                msg_origin['text'] = ''  # quitar el caption al mensaje original
                return msg_2nd

            msg_2nd = None
            caption = msg_data.get('text', '')
            if msg_data['ttype'] in ('file', 'audio'):  # para file y audio, solo se quita el texto si lo tiene
                msg_data['text'] = ''
            elif msg_data['ttype'] == 'product':
                # TODO FIX get_product_caption
                prod_id, caption = self.get_product_caption(msg_data.get('res_id'), caption)
                attach = get_binary_attach(self.env, 'product.product', prod_id.id,
                                           'image_chat', fields_ret=['id'])
                if caption and attach:  # se tiene que crear un mensaje nuevo
                    msg_2nd = create_text_message(msg_data, caption)  # nuevo mensaje
                    msg_data['show_product_text'] = False
            elif msg_data['ttype'] in ('image', 'video'):  # se crea otro mensaje de tipo texto con el caption
                if caption:
                    msg_2nd = create_text_message(msg_data, caption)
                msg_data['text'] = ''
            if msg_2nd:  # enviar y notificar el mensaje
                message_obj = self.env['acrux.chat.message'].create(msg_2nd)
                message_obj.message_send()
                data_to_send = self.build_dict(limit=0)
                data_to_send[0]['messages'] = message_obj.get_js_dict()
                self._sendone(self.get_channel_to_many(), 'update_conversation', data_to_send)

        return msg_data

    def send_message_bus_release(self, msg_data, back_status, check_access=True):
        ''' msg_data = {
                'ttype': 'info',
                'from_me': True,
                'contact_id': self.conversation_id,
                'res_model': False,
                'res_id': False,
                'text': 'un texto',
            }
        '''
        self.ensure_one()
        self = self
        self.send_message(msg_data, check_access)
        if back_status == 'new':
            self.set_to_new()
        elif back_status == 'done':
            self.set_to_done()

    @api.model
    def get_fields_to_read(self):
        activity_fields = ['activity_exception_decoration', 'activity_exception_icon', 'activity_state',
                           'activity_summary', 'activity_type_icon', 'activity_type_id', 'activity_ids']
        return ['id', 'name', 'number', 'agent_id', 'status', 'team_id', 'image_url',
                'number_format', 'border_color', 'res_partner_id', 'connector_id',
                'last_activity', 'desk_notify', 'connector_type', 'show_icon', 'allow_signing',
                'tag_ids', 'note', 'allowed_lang_ids', 'conv_type', 'oldes_activity_date',
                'unanswered'] + activity_fields

    def build_dict(self, limit, offset=0, field_names: List[str] = None):
        '''
        :todo se debe optimizar la consulta SQL
        '''
        AcruxChatMessages = self.env['acrux.chat.message']
        Tags = self.env['acrux.chat.conversation.tag']
        if not field_names:
            field_names = self.get_fields_to_read()
        conversations = self.read(field_names)
        if limit > 0:
            for conv in conversations:
                message_id = AcruxChatMessages.search([('contact_id', '=', conv['id'])],
                                                      limit=limit, offset=offset)
                message = message_id.get_js_dict()
                conv['messages'] = message
        for conv in conversations:
            if conv['tag_ids']:
                conv['tag_ids'] = Tags.browse(conv['tag_ids']).read(['id', 'name', 'color'])
        return conversations

    @api.model
    def search_active_conversation(self):
        ''' For present user '''
        if self.env.user.has_group('whatsapp_connector.group_chatroom_admin'):
            domain = ['|', ('status', '=', 'new'),
                      ('status', '=', 'current')]
        else:
            domain = ['|', ('status', '=', 'new'),
                      '&', ('status', '=', 'current'),
                      ('agent_id', '=', self.env.user.id)]
        conversations = self.search(domain)
        return conversations.ids

    @api.model
    def search_partner_from_number(self, conv_id):
        ResPartner = self.env['res.partner']
        if conv_id.connector_id.is_facebook_or_instagram():
            return ResPartner
        domain = [('company_id', 'in', [conv_id.connector_id.company_id.id, False]),
                  ('conv_standard_numbers', 'like', conv_id.number)]
        return ResPartner.search(domain)

    @api.model
    def search_conversation_by_partner_domain(self, partner_id):
        return [('res_partner_id', '=', partner_id),
                ('company_id', '=', self.env.company.id)]

    @api.model
    def search_conversation_by_partner(self, partner_id, limit):
        self = self.with_context(acrux_from_chatter=True)
        conversations = self.search(self.search_conversation_by_partner_domain(partner_id))
        return conversations.build_dict(limit)

    def conversation_send_read(self):
        ''' Send notification of read message. '''
        Message = self.env['acrux.chat.message']
        for conv_id in self:
            conn_id = conv_id.connector_id
            if conn_id.ca_status and conn_id.connector_type == 'apichat.io':
                conv_id.mark_conversation_read({'phone': conv_id.number, 'chat_type': conv_id.conv_type})
            elif conn_id.is_owner_facebook():
                if conn_id.ca_status and conn_id.is_facebook():
                    message_id = Message.search([('contact_id', '=', conv_id.id)], limit=1)
                    if message_id and message_id.message_check_time(raise_on_error=False):
                        conv_id.mark_conversation_read({'phone': conv_id.number})
                elif conn_id.ca_status and conn_id.is_waba_extern():
                    message_ids = Message.search_read([('contact_id', '=', conv_id.id),
                                                       ('from_me', '=', False),
                                                       ('read_date', '=', False)], fields=['msgid'], limit=20)
                    if message_ids:
                        message_ids = list(map(lambda msg: msg['msgid'], message_ids))
                        conv_id.mark_conversation_read({'message_ids': message_ids}, timeout=30)

    def mark_conversation_read(self, data, timeout=5):
        self.ensure_one()
        try:
            self.env.cr.execute('''
                                UPDATE acrux_chat_message
                                SET read_date = now()
                                WHERE read_date IS NULL
                                  AND contact_id IN %(conv_id)s
                                ''', {'conv_id': tuple(self.ids)})
            if bool(self.env.cr.rowcount):
                self.env.cr.commit()
                self.connector_id.ca_request('msg_set_read', data, timeout=timeout, ignore_exception=True)
        except Exception as _e:
            print(_e)

    def conversation_verify_to_new(self, conn_id):
        if conn_id.time_to_reasign:
            date_to_news = date_timedelta(minutes=-conn_id.time_to_reasign)
            return self.filtered(lambda x: x.status == 'current' and
                                           x.last_received_first and
                                           x.write_date < date_to_news)
        else:
            return self.env['acrux.chat.conversation']

    def conversation_verify_to_done(self, conn_id):
        if conn_id.time_to_done:
            date_to_done = date_timedelta(days=-conn_id.time_to_done)
            ret = self.filtered(lambda x: x.write_date < date_to_done)
            return ret
        else:
            return self.env['acrux.chat.conversation']

    @api.model
    def conversation_verify(self):
        ''' Call from cron or direct '''
        Connector = self.env['acrux.chat.connector'].sudo()
        to_done_ids = to_news_ids = self.env['acrux.chat.conversation']
        for conn_id in Connector.search([]):
            sctx = self.sudo().with_context(tz=conn_id.tz,
                                            lang=conn_id.company_id.partner_id.lang,
                                            allowed_company_ids=[conn_id.company_id.id])
            add_ids = sctx.search([('connector_id', '=', conn_id.id),
                                   ('status', '!=', 'done')])
            to_news = add_ids.conversation_verify_to_new(conn_id)
            to_done = (add_ids - to_news).conversation_verify_to_done(conn_id)
            to_done_ids |= to_done
            to_news_ids |= to_news
            all_ids = to_done | to_news
            if len(all_ids):
                conv_delete_ids = all_ids.read(['id', 'agent_id'])
                for to_x in all_ids:
                    to_x.event_create('unanswered', user_id=to_x.agent_id)
                to_done.set_to_done()
                to_news.set_to_new()
                notifications = []
                notifications.append((all_ids[0].get_channel_to_many(), 'update_conversation', conv_delete_ids))
                all_ids._sendmany(notifications)
                to_news._sendone(all_ids[0].get_channel_to_many(), 'new_messages', to_news.build_dict(22))
        _logger.info('________ | conversation_verify: %s to new, %s to done' % (len(to_news_ids), len(to_done_ids)))

    def block_conversation(self):
        self.ensure_one()
        data = None
        if self.status in ['new', 'done']:
            channel = self.get_channel_to_many()
            self.set_to_current()
            data = self.build_dict(2)
            self._sendone(channel, 'update_conversation', data)
        else:
            if (self.agent_id.id != self.env.user.id and
                    not self.env.user.has_group('whatsapp_connector.group_chatroom_admin')):
                raise ValidationError(_('Customer is already being served for %s') % self.agent_id.name)
        return data if data else self.build_dict(2)

    def release_conversation(self):
        agent_id = self.agent_id
        self.set_to_done()
        self.update_conversation_bus()
        if self.env.user != agent_id:
            self.notify_discuss_to_user(agent_id, 'I closed this chat:')

    @api.model
    def get_message_fields_to_read(self):
        return self.env['acrux.chat.message'].get_fields_to_read()

    @api.model
    def get_attachment_fields_to_read(self):
        return ['id', 'checksum', 'mimetype', 'display_name', 'url', 'name',
                'res_model', 'res_field', 'res_id']

    @api.model
    def get_product_fields_to_read(self):
        fields_search = ['id', 'display_name', 'lst_price', 'uom_id',
                         'write_date', 'product_tmpl_id', 'name', 'type', 'default_code']
        if 'qty_available' in self.env['product.product']._fields:
            fields_search.append('qty_available')
        return fields_search

    @api.model
    def search_product(self, string):
        ProductProduct = self.env['product.product']
        domain = [('sale_ok', '=', True)]
        if string:
            if string.startswith('/cat '):
                domain += [('categ_id.complete_name', 'ilike', string[5:].strip())]
            else:
                domain += ['|', ('name', 'ilike', string), ('default_code', 'ilike', string)]
        fields_search = self.get_product_fields_to_read()
        out = ProductProduct.search_read(domain, fields_search, order='name, list_price', limit=32)
        return out

    def init_and_notify(self):
        self.ensure_one()
        back_status = self.status
        self.block_conversation()
        data_to_send = self.build_dict(22)
        if back_status != 'current':
            self._sendone(self.get_channel_to_many(), 'update_conversation', data_to_send)
        self._sendone(self.get_channel_to_one(self.env.user), 'init_conversation', data_to_send)

    @api.model
    def conversation_create(self, partner_id, connector_id, number):
        Connector = self.env['acrux.chat.connector']
        if connector_id:
            connector_id = Connector.browse(connector_id)
        else:
            connector_id = Connector.search([], limit=1)
        number = connector_id.clean_id(number)
        connector_id.assert_id(number)
        vals = {
            'name': number,
            'number': number,
            'connector_id': connector_id.id,
            'status': 'done'
        }
        if partner_id:
            vals['name'] = partner_id.name
            vals['res_partner_id'] = partner_id.id
        conv_id = self.create(vals)
        return conv_id

    @api.model
    def contact_update(self, connector_id, data):
        number = data.get('number', '')
        image_url = data.get('image_url') or ''
        if number and image_url:
            conv_id = self.search([('number', '=', number),
                                   ('connector_id', '=', connector_id.id)])
            if conv_id:
                if image_url and image_url.startswith('http'):
                    raw_image = get_image_from_url(image_url)
                    conv_id.image_128 = raw_image

    @api.model
    def _get_message_allowed_types(self):
        return ['text', 'image', 'audio', 'video', 'file', 'location', 'sticker', 'url', 'voice', 'contact']

    import re

    def replace_mentions_with_names(self, connector, text, mentions):
        whapi_helper = WhapiHelper()
        at_placeholders = re.findall(r'@(\d+)', text)

        for i, placeholder in enumerate(at_placeholders):
            if i < len(mentions):
                real_id = mentions[i]  # This is the real WhatsApp ID
                number = real_id.split('@')[0]  # usually just the ID
                name = whapi_helper.get_contact_name(connector, number)
                if not name:
                    name = number  # fallback to the number if name not found
                text = text.replace(f'@{placeholder}', f'@{name}', 1)

        return text

    def handle_contact_message(message):
        chat_id = message['chat_id']
        contact_info = message['contact']
        vcard_str = contact_info['vcard']

        try:
            vcard = vobject.readOne(vcard_str)

            full_name = getattr(vcard, 'fn', None).value if hasattr(vcard, 'fn') else contact_info.get('name')
            tel = getattr(vcard, 'tel', None).value if hasattr(vcard, 'tel') else None
            waid = None

            # Parse the raw TEL field to extract the waid (WhatsApp ID)
            for line in vcard_str.splitlines():
                if "TEL" in line and "waid=" in line:
                    waid = line.split("waid=")[-1].split(":")[0]
                    break

            print(f"Received contact:\nName: {full_name}\nPhone: {tel}\nWA ID: {waid}")

            # You can now store this contact or display it in the UI
        except Exception as e:
            print("Error parsing vCard:", e)

    @api.model
    def parse_message_receive(self, connector_id, message):
        ttype = message.get('type')
        text = message.get('text', {}).get('body')
        text = text or ''

        mentions = message.get('context', {}).get('mentions', [])
        if mentions:
            text = self.replace_mentions_with_names(connector_id, text, mentions)

        if ttype == 'document':
            ttype = 'file'
        if ttype not in self._get_message_allowed_types():
            return None
        if message.get('timestamp'):
            date_msg = datetime.fromtimestamp(message.get('timestamp'))
        else:
            date_msg = fields.Datetime.now()
        out = {
            'ttype': ttype,
            'connector_id': connector_id.id,
            'name': message.get('from_name') or message.get('from'),
            'chat_id': message.get('chat_id'),
            'chat_name': message.get('chat_name'),
            'msgid': message.get('id', False),
            'number': message.get('from', ''),
            'message': text.strip(),
            'filename': message.get('filename', ''),
            'url': message.get('url', ''),
            'time': date_msg,
            'conv_type': 'normal',
            'quote_msg_id': message.get('context', {}).get('quoted_id'),
            'quote_msg_text': message.get('context', {}).get('quoted_content', {}).get('body', ''),
            'from_me': message.get('from_me')
        }

        if ttype == 'contact' and message.get('contact'):
            contact = message['contact']
            out['ttype'] = 'contact'
            out['contact_name'] = contact.get('name')

            # Extract phone number from vCard
            vcard_str = contact.get('vcard', '')
            for line in vcard_str.splitlines():
                if "TEL" in line and "waid=" in line:
                    waid = line.split("waid=")[-1].split(":")[0]
                    phone = line.split(":")[-1]
                    out['contact_number'] = waid
                    out['message'] = f"{contact.get('name', '')} ({phone.strip()})"
                    break
            else:
                out['message'] = contact.get('name', '')

        if message.get('metadata'):
            out['metadata'] = message['metadata']

        if message.get('document'):
            out['media_id'] = message['document']['id']
            out['filename'] = message['document']['file_name']

        if message.get('sticker'):
            out['media_id'] = message['sticker']['id']

        if message.get('image'):
            out['media_id'] = message['image']['id']
            out['filename'] = message['image']['id']
            out['message'] = message.get('image').get('caption', '')

        if message.get('voice'):
            out['media_id'] = message['voice']['id']
            out['ttype'] = 'audio'

        if message.get('audio'):
            out['media_id'] = message['audio']['id']

        if message.get('video'):
            out['media_id'] = message['video']['id']

        if connector_id.connector_type == 'apichat.io' and message.get('id'):
            out['from_me'] = message['id'].split('_')[0] == 'true'
            if message.get('id'):
                if '@l.us' in message['id']:
                    out['conv_type'] = 'private'
                elif '@g.us' in message['id']:
                    out['conv_type'] = 'group'
                    out['contact_name'] = message['name']
                    try:
                        connector_id.assert_id(message['author'])
                        out['contact_number'] = connector_id.format_id(message['author'])
                    except Exception:
                        out['contact_number'] = message.get('author')
        return out

    def get_instagram_sender_details(self, sender_id, page_access_token):
        """Fetch sender's name from Facebook Graph API."""
        url = f"https://graph.facebook.com/v22.0/{sender_id}?access_token={page_access_token}"

        try:
            response = requests.get(url)
            response.raise_for_status()  # Raise an error for bad responses (4xx, 5xx)
            data = response.json()
            return {
                "name": data.get("name", "Unknown User"),
                "profile_picture": data.get('profile_pic', {})
            }
        except requests.RequestException as e:
            print(f"Error fetching sender name: {e}")
            return "Unknown User"

    def get_facebook_contact_details(self, sender_id, page_access_token):
        """Fetch sender's name from Facebook Graph API."""
        url = f"{FACEBOOK_GRAPH_API_URL}/{sender_id}?fields=first_name,last_name,name,picture&access_token={page_access_token}"

        try:
            response = requests.get(url)
            response.raise_for_status()  # Raise an error for bad responses (4xx, 5xx)
            data = response.json()
            return {
                "name": data.get("name", "Unknown User"),
                "profile_picture": data.get("picture", {}).get("data", {}).get("url", "")
            }
        except requests.RequestException as e:
            _logger.error(f"Error fetching sender name: {e}")
            return {
                "name": "Unknown User",
                "profile_picture": ""
            }

    @api.model
    def parse_messenger_message_receive(self, connector_id, message):
        """Parse incoming Facebook messages."""

        msg_data = message.get("message", {})
        receiver_id = message.get("recipient", {}).get("id", "")
        sender_id = message.get("sender", {}).get("id", "")
        timestamp = message.get("timestamp")
        from_me = connector_id.uuid == message.get("sender", {}).get("id", "")

        contact_details = None
        if from_me:
            contact_details = self.get_facebook_contact_details(receiver_id, connector_id.token)
            contact_details['id'] = receiver_id
        else:
            contact_details = self.get_facebook_contact_details(sender_id, connector_id.token)
            contact_details['id'] = sender_id

        return {
            "ttype": "text",
            "connector_id": connector_id.id,
            "name": contact_details["name"],
            "image_url": contact_details["profile_picture"],
            "msgid": msg_data.get("mid", ""),
            "number": contact_details['id'],
            "message": msg_data.get("text", "").strip(),
            "filename": "",
            "url": "",
            "time": datetime.fromtimestamp(timestamp / 1000) if timestamp else datetime.now(),
            "conv_type": "normal",
            "quote_msg_id": None,
            "metadata": message.get("metadata", {}),
            'chat_id': contact_details['id'],
            'from_me': from_me
        }

    def parse_instagram_message_receive(self, connector_id, message):
        """Parse incoming Facebook messages."""
        msg_data = message.get("message", {})
        sender = message.get("sender", {}).get("id", "")
        timestamp = message.get("timestamp")

        sender_details = self.get_instagram_sender_details(sender, connector_id.token)
        return {
            "ttype": "text",
            "connector_id": connector_id.id,
            "name": sender_details["name"],
            "image_url": sender_details["profile_picture"],
            "msgid": msg_data.get("mid", ""),
            "number": sender,
            "message": msg_data.get("text", "").strip(),
            "filename": "",
            "url": "",
            "time": datetime.fromtimestamp(timestamp / 1000) if timestamp else datetime.now(),
            "conv_type": "normal",
            "quote_msg_id": None,
            "metadata": message.get("metadata", {}),
            'chat_id': sender,
        }

    @api.model
    def parse_contact_receive(self, connector_id, data):
        data['number'] = connector_id.clean_id(data.get('number', ''))
        return data

    @api.model
    def parse_event_receive(self, connector_id, event):
        if event.get('type') == 'failed':
            out = {
                'type': event.get('type'),
                'msgid': event.get('msgid'),
                'reason': event.get('txt'),
            }
        elif event.get('type') == 'phone-status':
            out = event
        else:
            out = event
        return out

    @api.model
    def new_webhook_event(self, connector_id, event):
        ttype = event.get('type')
        if ttype == 'failed':
            if event['msgid'] and event['reason']:
                self.new_message_event(connector_id, event['msgid'], event)
            _logger.warning(event)
        elif ttype == 'phone-status':
            connector_id.ca_status_change(event.get('status'))
        elif event.get('type') == 'opt_update' and connector_id.connector_type == 'gupshup':
            self.update_opt_in(connector_id, event)
        elif ttype == 'face-status':
            connector_id.process_facebook_get_status(event)
        elif ttype == 'deleted':
            self.new_message_event(connector_id, event['msgid'], event)

    @api.model
    def parse_status_receive(self, connector_id, data):
        ''' Parse status updates (delivery/read receipts) from Whapi.cloud '''
        try:
            _logger.info('📋 Parsing status update: %s', data)

            # Whapi.cloud status structure
            status_data = {
                'message_id': data.get('id'),
                'status': data.get('status'),  # sent, delivered, read, failed
                'timestamp': data.get('timestamp'),
                'from': data.get('from'),
                'to': data.get('to'),
                'error': data.get('error'),
                'connector_id': connector_id
            }

            return status_data
        except Exception as e:
            _logger.error('❌ Error parsing status update: %s', str(e))
            return None

    @api.model
    def new_status_event(self, connector_id, data):
        ''' Process message status updates '''
        _logger.info('📋 Processing status event: %s', data)

        try:
            message_id = data.get('message_id')
            status = data.get('status')

            if not message_id or not status:
                _logger.warning('⚠️ Invalid status data: missing message_id or status')
                return

            # Find the message by msgid
            Message = self.env['acrux.chat.message']
            message = Message.search([('msgid', '=', message_id)], limit=1)

            if message:
                # Update message status
                if status == 'delivered':
                    message.write({'delivered': True})
                elif status == 'read':
                    message.write({'delivered': True, 'is_read': True})
                elif status == 'failed':
                    message.write({'error_msg': data.get('error', 'Message failed')})

                _logger.info('✅ Updated message %s status to %s', message_id, status)
            else:
                _logger.warning('⚠️ Message not found for status update: %s', message_id)

        except Exception as e:
            _logger.error('❌ Error processing status event: %s', str(e))

    @api.model
    def parse_presence_receive(self, connector_id, data):
        ''' Parse presence updates (online/offline) from Whapi.cloud '''
        try:
            _logger.info('👤 Parsing presence update: %s', data)

            presence_data = {
                'from': data.get('from'),
                'presence': data.get('presence'),  # available, unavailable
                'last_seen': data.get('last_seen'),
                'timestamp': data.get('timestamp'),
                'connector_id': connector_id
            }

            return presence_data
        except Exception as e:
            _logger.error('❌ Error parsing presence update: %s', str(e))
            return None

    @api.model
    def new_presence_event(self, connector_id, data):
        ''' Process presence updates '''
        _logger.info('👤 Processing presence event: %s', data)

        try:
            from_number = data.get('from')
            presence = data.get('presence')

            if not from_number:
                _logger.warning('⚠️ Invalid presence data: missing from number')
                return

            # Find or create conversation
            conversation = self._get_conversation_by_number(connector_id, from_number)

            if conversation:
                # Update conversation presence
                conversation.write({
                    'last_activity': fields.Datetime.now(),
                    'status': 'current' if presence == 'available' else conversation.status
                })

                # Trigger UI update via bus
                self.env['bus.bus']._sendone(
                    f'acrux_chat_conversation_{conversation.id}',
                    'presence_update',
                    {
                        'conversation_id': conversation.id,
                        'presence': presence,
                        'timestamp': data.get('timestamp')
                    }
                )

                _logger.info('✅ Updated presence for conversation %s: %s', conversation.id, presence)
            else:
                _logger.warning('⚠️ Conversation not found for presence update: %s', from_number)

        except Exception as e:
            _logger.error('❌ Error processing presence event: %s', str(e))

    @api.model
    def parse_typing_receive(self, connector_id, data):
        ''' Parse typing indicators from Whapi.cloud '''
        try:
            _logger.info('⌨️ Parsing typing indicator: %s', data)

            typing_data = {
                'from': data.get('from'),
                'type': data.get('type'),  # typing, recording, paused
                'timestamp': data.get('timestamp'),
                'connector_id': connector_id
            }

            return typing_data
        except Exception as e:
            _logger.error('❌ Error parsing typing indicator: %s', str(e))
            return None

    @api.model
    def new_typing_event(self, connector_id, data):
        ''' Process typing indicators '''
        _logger.info('⌨️ Processing typing event: %s', data)

        try:
            from_number = data.get('from')
            typing_type = data.get('type')

            if not from_number or not typing_type:
                _logger.warning('⚠️ Invalid typing data: missing from or type')
                return

            # Find conversation
            conversation = self._get_conversation_by_number(connector_id, from_number)

            if conversation:
                # Send typing indicator to UI via bus
                self.env['bus.bus']._sendone(
                    f'acrux_chat_conversation_{conversation.id}',
                    'typing_indicator',
                    {
                        'conversation_id': conversation.id,
                        'type': typing_type,  # typing, recording, paused
                        'timestamp': data.get('timestamp'),
                        'is_typing': typing_type in ['typing', 'recording']
                    }
                )

                _logger.info('✅ Sent typing indicator for conversation %s: %s', conversation.id, typing_type)
            else:
                _logger.warning('⚠️ Conversation not found for typing indicator: %s', from_number)

        except Exception as e:
            _logger.error('❌ Error processing typing event: %s', str(e))

    def _get_conversation_by_number(self, connector_id, number):
        ''' Helper method to find conversation by phone number '''
        return self.search([
            ('connector_id', '=', connector_id.id if hasattr(connector_id, 'id') else connector_id),
            ('number', '=', number)
        ], limit=1)

    @api.model
    def check_object_reference(self, postfix, view):
        return self.sudo().env['ir.model.data'].check_object_reference('whatsapp_connector%s' % (postfix or ''), view)

    def delegate_conversation(self):
        self.ensure_one()
        if self.status != 'new':
            self.with_context(ignore_agent_id=True).set_to_new()
        if self.tmp_agent_id:
            if self.connector_id.notify_discuss:
                self.notify_discuss_to_user(self.tmp_agent_id, 'I delegated a Chat to you.')
            self.with_user(self.tmp_agent_id).set_to_current()
        notifications = []
        data = self.build_dict(22)
        if self.tmp_agent_id:
            self.tmp_agent_id = False
            notifications.append((self.get_channel_to_many(), 'update_conversation', data.copy()))
            for r in data:
                r['assigned'] = True
            notifications.append((self.get_channel_to_one(), 'update_conversation', data))  # para mantener el assigned
        else:
            notifications.append((self.get_channel_to_many(), 'new_messages', data))
        self._sendmany(notifications)

    def toggle_opt_in(self):
        self.ensure_one()
        self.sent_opt_in = True
        self.is_waba_opt_in = not self.is_waba_opt_in
        if not self.mute_opt_in and not self.is_waba_opt_in:
            data_to_send = {
                'conv': self.id,
                'name': self.name_get()[0][1],
                'opt_in': self.is_waba_opt_in
            }
            self._sendone(self.get_channel_to_many(), 'opt_in', data_to_send)
        # opt-in is removed from gupshup
        # data = {
        #     'number': self.number,
        #     'opt_in': not self.is_waba_opt_in
        # }
        # self.connector_id.ca_request('opt_in', data)

    @api.model
    def update_opt_in(self, connector_id, event):
        conv = self.search([('connector_id', '=', connector_id.id),
                            ('number', '=', connector_id.clean_id(event['number']))], limit=1)
        if conv:
            conv.is_waba_opt_in = event['opt_in']
            if not conv.mute_opt_in and not conv.is_waba_opt_in:
                data_to_send = {
                    'conv': conv.id,
                    'name': conv.name_get()[0][1],
                    'opt_in': event['opt_in']
                }
                conv._sendone(conv.get_channel_to_many(), 'opt_in', data_to_send)

    def refresh_api_data(self):
        self.ensure_one()
        self.update_conversation()
        self.update_conversation_bus()

    @api.depends('activity_date_deadline')
    def _compute_kanban_state(self):
        today = date.today()
        for conv in self:
            kanban_state = 'grey'
            if conv.activity_date_deadline:
                lead_date = fields.Date.from_string(conv.activity_date_deadline)
                if lead_date >= today:
                    kanban_state = 'green'
                else:
                    kanban_state = 'red'
            conv.kanban_state = kanban_state

    @api.model
    def _read_group_stage_ids(self, stages, domain):
        """ Always display all stages """
        return stages.search([])

    def close_from_ui(self):
        self.ensure_one()
        if self.status == 'new' or (self.status == 'current' and self.agent_id == self.env.user):
            self.release_conversation()

    @api.model
    def web_read_group(self, domain, fields, groupby, limit=None, offset=0, orderby=False, lazy=True):
        out = super().web_read_group(domain, fields, groupby, limit, offset, orderby, lazy)
        if self.env.context.get('chatroom_fold_null_group') and out.get('length') > 0:
            for group in out['groups']:
                if group.get('stage_id', None) is False:
                    group['__fold'] = True
        return out

    @api.model
    def get_config_parameters(self):
        Config = self.env['ir.config_parameter'].sudo()
        return {
            'chatroom_tab_orientation': Config.get_param('chatroom_tab_orientation'),
            'chatroom_batch_process': Config.get_param('chatroom_batch_process'),
        }

    @api.depends('activity_ids', 'my_activity_date_deadline')
    def _compute_oldest_activity(self):
        for record in self:
            record.oldes_activity_date = record.my_activity_date_deadline
            record.update_conversation_bus()

    @api.model
    def get_domain_filtering_js(self, search, filters, alias=None):
        alias = f'{alias}.' if alias else ''
        js_domain = []
        if filters.get('filterActivity'):
            js_domain.extend(['&', [f'{alias}oldes_activity_date', '<', fields.Datetime.now()]])
        if filters.get('filterPending'):
            js_domain.extend(['&', [f'{alias}unanswered', '=', True]])
        if filters.get('filterMine'):
            js_domain.extend([
                '&', '|',
                [f'{alias}agent_id', '=', self.env.user.id],
                [f'{alias}status', 'in', ('new', 'down')]])
        if search:
            js_domain.extend([
                '|', '|',
                [f'{alias}name', 'ilike', search],
                [f'{alias}number_format', 'ilike', search],
                [f'{alias}number', 'ilike', search]
            ])
        js_domain.append([f'{alias}id', '!=', False])  # para hacer más fácil el código
        return js_domain

    @api.model
    def get_chats_filtering_js(self, search, filters):
        domain = self.get_domain_filtering_js(search, filters)
        chats = self.search(domain, limit=80)
        chats = chats.build_dict(limit=0)
        return self.format_filtering_js_result(chats)

    @api.model
    def get_messages_filtering_js(self, search, filters):
        if not search:
            return []
        Messages = self.env['acrux.chat.message']
        domain = self.get_domain_filtering_js(None, filters, alias='contact_id')
        domain = expression.AND([domain, [('ttype', 'not ilike', 'info%')]])
        cond = [
            '|',
            ['text', 'ilike', search],
            ['traduction', 'ilike', search]
        ]
        domain = expression.AND([domain, cond])
        messages_data: list = Messages.search_read(domain, fields=['id', 'text', 'traduction', 'contact_id'], limit=80)
        messages_chat = list(map(lambda conv: conv['contact_id'][0], messages_data))
        messages_chat = self.browse(messages_chat).build_dict(limit=0)
        messages_chat = {conv['id']: conv for conv in messages_chat}
        messages = []
        for msg in messages_data:
            text = msg['text'] or msg['traduction']
            if msg['text'] and search in msg['text']:
                text = msg['text']
            elif msg['traduction'] and search in msg['traduction']:
                text = msg['traduction']
            conv = messages_chat[msg['contact_id'][0]].copy()
            conv['free_text'] = text
            conv['number_format'] = ''
            messages.append(conv)
        return self.format_filtering_js_result(messages)

    @api.model
    def conversation_filtering_js(self, search, filters={}):
        return {
            _('Chats'): self.get_chats_filtering_js(search, filters),
            _('Messages'): self.get_messages_filtering_js(search, filters),
        }

    @api.model
    def format_filtering_js_result(self, res) -> list:
        chat_keys = [
            _('Mine'),
            _('Attending'),
            _('Waiting'),
            _('Resume'),
        ]
        chats = {key: [] for key in chat_keys}
        for record in res:
            if record['agent_id'] and record['agent_id'][0] == self.env.user.id:
                chats[_('Mine')].append(record)
            elif record['status'] == 'current':
                chats[_('Attending')].append(record)
            elif record['status'] == 'new':
                chats[_('Waiting')].append(record)
            else:
                chats[_('Resume')].append(record)
        return list(map(lambda v: {'name': v, 'values': chats[v]}, filter(lambda e: chats[e], chat_keys)))

    def notify_discuss_to_user(self, user_id, msg, detail=True):
        if self.env.context.get('not_notify_discuss') or not user_id:
            return
        Channel = self.env['discuss.channel']
        channel_id = Channel.channel_get([user_id.partner_id.id])  # pin=False ?
        if channel_id:
            for rec in self:
                if rec.connector_id.notify_discuss:
                    if detail:
                        info = '<br/>Channel: %s<br/>Chat: %s' % (rec.connector_id.name, rec.name)
                        if not rec.connector_id.is_facebook_or_instagram():
                            info += '<br/>(%s)' % rec.number_format
                        msg += info
                    channel_id.message_post(body=Markup(msg),
                                            author_id=self.env.user.partner_id.id,
                                            message_type='comment',
                                            subtype_xmlid='mail.mt_comment')

    # AI Auto Reply Methods
    def _should_trigger_ai_reply(self, message):
        """Check if AI should respond to this message - TWO LEVEL CONTROL"""
        # Don't reply to our own messages
        if message.from_me:
            _logger.debug(f"🤖 AI SKIP: Message is from me (conversation_id={self.id})")
            return False

        # 🔍 Get Connector-level AI settings
        ai_settings_data = self.env['acrux.chat.ai.settings'].get_ai_config_for_connector(
            self.connector_id.id
        )

        if not ai_settings_data:
            _logger.debug(f"🤖 AI SKIP: No AI settings found for connector {self.connector_id.id} (conversation_id={self.id})")
            return False

        # 🎯 TWO-LEVEL CHECK: Both connector AND conversation must be enabled
        connector_ai_enabled = ai_settings_data.get('ai_auto_reply_enabled', False)
        conversation_ai_enabled = self.ai_auto_reply_enabled

        # 🔍 SIMPLE DEBUG: Show key values only
        _logger.info(f"🔍 AI CHECK: Connector={connector_ai_enabled}, Conversation={conversation_ai_enabled}, Customer={self.name}")

        # 📊 PRINT SCENARIO TABLE
        connector_status = "✅ True " if connector_ai_enabled else "❌ False"
        conversation_status = "✅ True " if conversation_ai_enabled else "❌ False"

        if connector_ai_enabled and conversation_ai_enabled:
            result = "✅ **AI WILL RESPOND**"
            description = "Both levels allow AI - AI will respond"
            log_level = "info"
        elif not connector_ai_enabled and not conversation_ai_enabled:
            result = "❌ **NO AI**"
            description = "Default safe state - both disabled"
            log_level = "debug"
        elif not connector_ai_enabled and conversation_ai_enabled:
            result = "❌ **NO AI**"
            description = "Admin blocks AI at connector level"
            log_level = "debug"
        else:  # connector_ai_enabled and not conversation_ai_enabled
            result = "❌ **NO AI**"
            description = "Agent hasn't enabled AI for this conversation"
            log_level = "debug"

        # 📋 PRINT THE SCENARIO TABLE
        table_message = f"""
🤖 AI CONTROL SCENARIO TABLE (Conversation ID: {self.id}, Customer: {self.name}):
|-----------|-------------|---------|-------------|
| Connector | Conversation| Result  | Description |
|-----------|-------------|---------|-------------|
| {connector_status} | {conversation_status} | {result} | {description} |
|-----------|-------------|---------|-------------|"""

        if log_level == "info":
            _logger.info(table_message)
        else:
            _logger.debug(table_message)

        if not (connector_ai_enabled and conversation_ai_enabled):
            return False

        # Only reply when offline if configured
        if ai_settings_data.get('ai_only_when_offline', True):
            # Check if conversation is currently attended by an active agent
            if self.status == 'current' and self.agent_id:
                _logger.debug(f"🤖 AI SKIP: Agent is online (status={self.status}, agent={self.agent_id.name}, conversation_id={self.id})")
                return False

        _logger.info(f"🤖 AI TRIGGER: Will respond to message in conversation {self.id} from {self.name}")
        return True

    def toggle_conversation_ai(self):
        """Toggle AI auto-reply for this specific conversation"""
        self.ensure_one()

        try:
            old_value = self.ai_auto_reply_enabled

            self.ai_auto_reply_enabled = not self.ai_auto_reply_enabled

            status = '🟢 ENABLED' if self.ai_auto_reply_enabled else '🔴 DISABLED'
            _logger.info(f"🤖 CONVERSATION AI TOGGLED: {old_value} → {self.ai_auto_reply_enabled} ({status}) for conversation {self.id} ({self.name})")

            # 🚨 DISABLED: Process last message feature (was causing duplicate responses)
            if not old_value and self.ai_auto_reply_enabled:
                _logger.info(f"🤖 AI RE-ENABLED: Conversation {self.id} - AI will respond to NEW messages only")
                # Note: Removed automatic processing of last message to prevent spam

        except Exception as toggle_error:
            _logger.error(f"🤖 TOGGLE ERROR: Failed to toggle AI for conversation {self.id}: {str(toggle_error)}")
            # Revert the change if something went wrong
            self.ai_auto_reply_enabled = old_value if 'old_value' in locals() else self.ai_auto_reply_enabled
            raise

        # 🔍 FINAL STATE CHECK
        _logger.info(f"🔍 FINAL STATE CHECK:")
        _logger.info(f"   🤖 Final AI State: {self.ai_auto_reply_enabled}")
        _logger.info(f"   📊 Status Message: {status}")

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('AI Status Updated'),
                'message': _('AI Auto-Reply for this conversation is now %s') % status,
                'type': 'success',
                'sticky': False,
            }
        }

    def check_conversation_ai_state(self):
        """Simple method to check conversation AI state"""
        self.ensure_one()
        _logger.info(f"🔍 CONVERSATION AI STATE CHECK:")
        _logger.info(f"   🆔 Conversation ID: {self.id}")
        _logger.info(f"   👤 Customer: {self.name}")
        _logger.info(f"   🤖 ai_auto_reply_enabled: {self.ai_auto_reply_enabled}")
        _logger.info(f"   📊 Field Type: {type(self.ai_auto_reply_enabled)}")

        return {
            'conversation_id': self.id,
            'customer_name': self.name,
            'ai_auto_reply_enabled': self.ai_auto_reply_enabled
        }

    def debug_ai_state(self):
        """Debug method to check current AI state"""
        self.ensure_one()

        # Get connector AI settings
        ai_settings_data = self.env['acrux.chat.ai.settings'].get_ai_config_for_connector(
            self.connector_id.id
        )

        connector_ai_enabled = ai_settings_data.get('ai_auto_reply_enabled', False) if ai_settings_data else False
        conversation_ai_enabled = self.ai_auto_reply_enabled

        _logger.info(f"🔍 AI STATE DEBUG FOR CONVERSATION {self.id}:")
        _logger.info(f"   👤 Customer: {self.name}")
        _logger.info(f"   📱 Phone: {self.number}")
        _logger.info(f"   🔌 Connector AI: {connector_ai_enabled}")
        _logger.info(f"   💬 Conversation AI: {conversation_ai_enabled}")
        _logger.info(f"   ✅ Both Enabled: {connector_ai_enabled and conversation_ai_enabled}")
        _logger.info(f"   📊 AI Settings Data: {ai_settings_data}")

        return {
            'connector_ai': connector_ai_enabled,
            'conversation_ai': conversation_ai_enabled,
            'both_enabled': connector_ai_enabled and conversation_ai_enabled,
            'ai_settings': ai_settings_data
        }

    def _process_last_message_for_ai(self):
        """Process the last customer message when AI is re-enabled"""
        self.ensure_one()

        _logger.info(f"🤖 SEARCHING FOR LAST MESSAGE: Conversation {self.id} ({self.name})")

        try:
            # Get all messages for debugging - use search instead of direct relation
            all_messages = self.env['acrux.chat.message'].search([
                ('contact_id', '=', self.id)
            ], order='date_message desc')
            _logger.info(f"🤖 TOTAL MESSAGES: {len(all_messages)} messages in conversation")

            # Get the last message from customer (not from us)
            last_customer_message = self.env['acrux.chat.message'].search([
                ('contact_id', '=', self.id),
                ('from_me', '=', False),
                ('ttype', '=', 'text')
            ], order='date_message desc', limit=1)

            _logger.info(f"🤖 CUSTOMER MESSAGES: {len(last_customer_message)} customer messages found")

            if last_customer_message:
                last_msg = last_customer_message[0]
                _logger.info(f"🤖 PROCESSING LAST MESSAGE: '{last_msg.text[:100]}...' (ID: {last_msg.id}, Date: {last_msg.date_message})")

                # Check if AI should respond to this message
                _logger.info(f"🤖 CHECKING AI CONDITIONS: About to call _should_trigger_ai_reply for message {last_msg.id}")

                if self._should_trigger_ai_reply(last_msg):
                    _logger.info(f"🤖 AI CONDITIONS MET: Generating AI reply for last message")
                    # Generate and send AI reply
                    self._generate_and_send_ai_reply(last_msg.text, last_msg)
                else:
                    _logger.warning(f"🤖 LAST MESSAGE SKIP: AI conditions not met for conversation {self.id}")
            else:
                _logger.warning(f"🤖 NO LAST MESSAGE: No customer messages found in conversation {self.id}")

        except Exception as e:
            _logger.error(f"🤖 ERROR in _process_last_message_for_ai: {str(e)}")
            _logger.error(f"🤖 ERROR details: Conversation {self.id}, Exception: {type(e).__name__}")

            # Debug: Show what messages we do have
            if all_messages:
                for i, msg in enumerate(all_messages[:3]):  # Show first 3 messages
                    _logger.info(f"🤖 MESSAGE {i+1}: from_me={msg.from_me}, ttype={msg.ttype}, text='{msg.text[:50]}...'")
            else:
                _logger.info(f"🤖 NO MESSAGES: Conversation {self.id} has no messages at all")

    def get_ai_debug_info(self):
        """Get detailed AI status information for debugging"""
        self.ensure_one()

        # Get AI service settings (for configuration only)
        ai_settings_data = self.env['acrux.chat.ai.settings'].get_ai_config_for_connector(
            self.connector_id.id
        )

        conversation_ai_enabled = self.ai_auto_reply_enabled
        has_service_config = bool(ai_settings_data)

        debug_info = {
            'conversation_id': self.id,
            'conversation_name': self.name,
            'connector_id': self.connector_id.id,
            'connector_name': self.connector_id.name,
            'conversation_ai_enabled': conversation_ai_enabled,
            'has_service_config': has_service_config,
            'will_ai_respond': conversation_ai_enabled and has_service_config,
            'agent_status': self.status,
            'has_agent': bool(self.agent_id),
            'agent_name': self.agent_id.name if self.agent_id else None,
            'ai_service_url': ai_settings_data.get('ai_service_url') if ai_settings_data else None,
        }

        _logger.info(f"🤖 AI DEBUG INFO: {debug_info}")
        return debug_info

    def _generate_and_send_ai_reply(self, incoming_text, incoming_message):
        """Generate and send AI reply using Django service - WITH ENHANCED DEBUGGING"""
        self.ensure_one()

        # 🔍 Log AI reply generation start
        _logger.info(f"🤖 AI REPLY GENERATION STARTED:")
        _logger.info(f"   📞 Conversation: {self.id} ({self.name})")
        _logger.info(f"   📱 Connector: {self.connector_id.id} ({self.connector_id.name})")
        _logger.info(f"   💬 Incoming Text: {incoming_text[:100]}...")
        _logger.info(f"   🎯 Conversation AI Enabled: {self.ai_auto_reply_enabled}")

        try:
            # Get AI settings record (not dict) for this connector
            ai_settings_model = self.env['acrux.chat.ai.settings']
            domain = [
                '|',
                ('connector_id', '=', self.connector_id.id),
                ('connector_id', '=', False)
            ]
            ai_settings = ai_settings_model.search(domain, order='connector_id desc', limit=1)

            if not ai_settings:
                _logger.warning(f"🤖 AI REPLY FAILED: No AI settings found for connector {self.connector_id.id}")
                return

            _logger.info(f"🤖 AI SETTINGS FOUND: {ai_settings.display_name} (connector_ai_enabled={ai_settings.ai_auto_reply_enabled})")
            
            from ..services.ai_reply_service import AIReplyService
            ai_service = AIReplyService(ai_settings)
            
            # Add delay if configured
            if ai_settings.ai_response_delay > 0:
                _logger.info(f"🤖 AI DELAY: Waiting {ai_settings.ai_response_delay} seconds before responding")
                import time
                time.sleep(ai_settings.ai_response_delay)

            # Get AI reply using your exact API format
            _logger.info(f"🤖 AI SERVICE CALL: Requesting AI reply from {ai_settings.ai_service_url}")

            try:
                ai_reply = ai_service.get_ai_reply(incoming_text, self)
                _logger.info(f"🤖 AI SERVICE SUCCESS: Received reply from AI service")
            except Exception as ai_error:
                _logger.warning(f"🤖 AI SERVICE ERROR: {str(ai_error)}")
                # 🚨 QUICK FALLBACK: Use simple response when AI service is down
                ai_reply = f"مرحبا! شكراً لرسالتك. أنا مساعد ذكي وقد استلمت رسالتك: '{incoming_text[:50]}...' سأساعدك في أقرب وقت ممكن!"
                _logger.info(f"🤖 FALLBACK REPLY: Using Arabic fallback response due to AI service unavailability")

            # 🔧 ENSURE WE HAVE A RESPONSE
            if not ai_reply:
                ai_reply = f"مرحبا! شكراً لرسالتك '{incoming_text[:50]}...' سأساعدك قريباً!"
                _logger.warning(f"🤖 EMPTY RESPONSE: AI service returned empty response, using fallback")

            if ai_reply:
                # Ensure ai_reply is a string
                if not isinstance(ai_reply, str):
                    ai_reply = str(ai_reply)

                _logger.info(f"🤖 AI REPLY SUCCESS: Generated reply: {ai_reply[:100]}...")

                # Use send_message with check_access=False for AI replies
                try:
                    self.send_message({
                        'text': ai_reply,
                        'ttype': 'text',
                        'from_me': True  # ✅ This marks it as a reply FROM the business
                    }, check_access=False)
                    _logger.info(f"🤖 MESSAGE SENT: AI reply sent successfully for conversation {self.id}")

                except Exception as send_error:
                    _logger.error(f"🤖 SEND ERROR: Failed to send AI reply: {str(send_error)}")

                    # Check if it's a trial limit error
                    if "trial version limit exceeded" in str(send_error) or "402" in str(send_error):
                        _logger.warning(f"🤖 TRIAL LIMIT: WhatsApp connector trial limit exceeded for conversation {self.id}")

                        # 🎯 TEST MODE: Create a local message for testing when API limit is reached
                        _logger.info(f"🤖 TEST MODE: Creating local AI reply message for testing purposes")
                        try:
                            # Create the message locally without sending via API
                            self.env['acrux.chat.message'].create({
                                'contact_id': self.id,
                                'text': f"[TEST MODE - API LIMIT] {ai_reply}",
                                'ttype': 'text',
                                'from_me': True,
                                'date_message': fields.Datetime.now(),
                            })
                            _logger.info(f"🤖 TEST MODE SUCCESS: Local AI reply created for conversation {self.id}")
                            return True
                        except Exception as local_error:
                            _logger.error(f"🤖 TEST MODE ERROR: Failed to create local message: {str(local_error)}")
                            return False

                    # For other errors, try a fallback approach
                    _logger.warning(f"🤖 SEND FALLBACK: Attempting fallback for conversation {self.id}")
                    return False

                # Update AI statistics
                ai_settings.update_ai_stats(success=True)

                # Log AI interaction for tracking
                _logger.info(f"🤖 AI REPLY SENT: Successfully sent AI reply to {self.number} (conversation_id={self.id})")

            elif ai_settings.ai_fallback_enabled and ai_settings.ai_fallback_message:
                _logger.warning(f"🤖 AI REPLY FAILED: No AI response, sending fallback message")

                # Send fallback message if AI failed (also bypass access check)
                self.send_message({
                    'text': ai_settings.ai_fallback_message,
                    'ttype': 'text',
                    'from_me': True  # ✅ This marks it as a reply FROM the business
                }, check_access=False)

                # Update AI statistics for failure
                ai_settings.update_ai_stats(success=False)

                _logger.warning(f"🤖 FALLBACK SENT: Fallback message sent to {self.number} (conversation_id={self.id})")
            else:
                _logger.error(f"🤖 AI REPLY FAILED: No AI response and no fallback configured (conversation_id={self.id})")
                
        except Exception as e:
            _logger.error(f"🤖 AI REPLY EXCEPTION: Error in AI auto-reply for conversation {self.id} ({self.name}): {str(e)}")

            # Try to send fallback message
            try:
                _logger.info(f"🤖 EXCEPTION FALLBACK: Attempting to send fallback message")
                ai_settings_model = self.env['acrux.chat.ai.settings']
                domain = [
                    '|',
                    ('connector_id', '=', self.connector_id.id),
                    ('connector_id', '=', False)
                ]
                ai_settings = ai_settings_model.search(domain, order='connector_id desc', limit=1)
                if ai_settings and ai_settings.ai_fallback_enabled and ai_settings.ai_fallback_message:
                    self.send_message({
                        'text': ai_settings.ai_fallback_message,
                        'ttype': 'text',
                        'from_me': True  # ✅ This marks it as a reply FROM the business
                    }, check_access=False)
                    ai_settings.update_ai_stats(success=False)
                    _logger.info(f"🤖 EXCEPTION FALLBACK SENT: Fallback message sent after exception")
                else:
                    _logger.error(f"🤖 EXCEPTION FALLBACK FAILED: No fallback available or not enabled")
            except Exception as fallback_error:
                _logger.error(f"🤖 EXCEPTION FALLBACK ERROR: Fallback also failed: {str(fallback_error)}")
                pass  # If fallback also fails, give up gracefully
    
    def get_ai_status(self):
        """Get AI status for this conversation - TWO LEVEL CONTROL"""
        ai_settings = self.env['acrux.chat.ai.settings'].get_ai_config_for_connector(
            self.connector_id.id
        )

        connector_ai_enabled = ai_settings.ai_auto_reply_enabled if ai_settings else False
        conversation_ai_enabled = self.ai_auto_reply_enabled
        both_enabled = connector_ai_enabled and conversation_ai_enabled

        if ai_settings:
            return {
                # Legacy field for backward compatibility
                'ai_enabled': both_enabled,  # This is what actually matters now

                # New two-level control fields
                'connector_ai_enabled': connector_ai_enabled,
                'conversation_ai_enabled': conversation_ai_enabled,
                'both_ai_enabled': both_enabled,
                'will_ai_respond': both_enabled,

                # Other settings
                'ai_only_offline': ai_settings.ai_only_when_offline,
                'current_status': self.status,
                'has_agent': bool(self.agent_id),
                'ai_service_url': ai_settings.ai_service_url,
                'connection_status': ai_settings.connection_status,

                # Debug info
                'conversation_id': self.id,
                'conversation_name': self.name,
                'connector_id': self.connector_id.id,
                'connector_name': self.connector_id.name,
            }
        else:
            return {
                # Legacy field
                'ai_enabled': False,

                # New two-level control fields
                'connector_ai_enabled': False,
                'conversation_ai_enabled': conversation_ai_enabled,
                'both_ai_enabled': False,
                'will_ai_respond': False,

                # Other settings
                'ai_only_offline': True,
                'current_status': self.status,
                'has_agent': bool(self.agent_id),
                'ai_service_url': None,
                'connection_status': 'not_tested',

                # Debug info
                'conversation_id': self.id,
                'conversation_name': self.name,
                'connector_id': self.connector_id.id if self.connector_id else None,
                'connector_name': self.connector_id.name if self.connector_id else None,
            }

    def delete_message(self, msg_id, for_me):
        self.ensure_one()
        AcruxChatMessages = self.env['acrux.chat.message']
        message_obj = AcruxChatMessages.browse(msg_id)
        self.connector_id.ca_request('delete_message', data={'MessageID': message_obj.msgid, })
        message_obj.write({'date_delete': fields.Datetime.now()})
        message_dict = message_obj.get_js_dict()
        self._sendone(self.get_channel_to_many(), 'update_conversation', [{
            'id': self.id,
            'messages': message_dict,
        }])
        return message_dict
