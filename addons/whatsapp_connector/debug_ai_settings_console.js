/* 
AI Settings Debug Console Commands
Copy and paste these commands into your browser console (F12) when you're on the WhatsApp connector page
*/

// 1. Test AI Settings Model Access
async function testAiSettingsModel() {
    console.log('🔍 Testing AI Settings Model Access...');
    
    try {
        const result = await odoo.env.services.rpc({
            model: 'acrux.chat.ai.settings',
            method: 'search_read',
            args: [[], ['id', 'ai_auto_reply_enabled', 'display_name', 'connector_id']],
        });
        
        console.log('✅ AI Settings found:', result);
        return result;
    } catch (error) {
        console.error('❌ AI Settings model error:', error);
        return null;
    }
}

// 2. Test Creating AI Settings for Connector
async function createAiSettingsForConnector(connectorId = 1) {
    console.log(`🔧 Creating AI Settings for connector ${connectorId}...`);
    
    try {
        const result = await odoo.env.services.rpc({
            model: 'acrux.chat.ai.settings',
            method: 'get_ai_config_for_connector',
            args: [connectorId],
        });
        
        console.log('✅ AI Config retrieved/created:', result);
        return result;
    } catch (error) {
        console.error('❌ Failed to get AI config:', error);
        return null;
    }
}

// 3. Test Toggle AI Settings
async function toggleAiSettings(settingId) {
    console.log(`🔄 Toggling AI settings ${settingId}...`);
    
    try {
        const result = await odoo.env.services.rpc({
            model: 'acrux.chat.ai.settings',
            method: 'toggle_ai_auto_reply',
            args: [settingId],
        });
        
        console.log('✅ AI Toggle result:', result);
        return result;
    } catch (error) {
        console.error('❌ Failed to toggle AI:', error);
        return null;
    }
}

// 4. Test Connection to Django AI Service
async function testAiConnection(settingId) {
    console.log(`🤖 Testing AI connection for setting ${settingId}...`);
    
    try {
        const result = await odoo.env.services.rpc({
            model: 'acrux.chat.ai.settings',
            method: 'test_ai_connection',
            args: [settingId],
        });
        
        console.log('✅ AI Connection test result:', result);
        return result;
    } catch (error) {
        console.error('❌ AI Connection test failed:', error);
        return null;
    }
}

// 5. Complete Debug Sequence
async function debugAiIntegration() {
    console.log('🚀 Starting Complete AI Integration Debug...');
    console.log('================================================');
    
    // Step 1: Check AI Settings
    const settings = await testAiSettingsModel();
    if (!settings || settings.length === 0) {
        console.log('⚠️ No AI settings found, creating default...');
        const config = await createAiSettingsForConnector(1);
        if (config) {
            console.log('✅ Default AI config created');
        }
    } else {
        console.log(`✅ Found ${settings.length} AI settings`);
    }
    
    // Step 2: Test toggle functionality
    const firstSetting = settings && settings.length > 0 ? settings[0] : await createAiSettingsForConnector(1);
    if (firstSetting) {
        console.log(`🔄 Testing toggle for setting ID: ${firstSetting.id}`);
        await toggleAiSettings(firstSetting.id);
        
        // Test connection
        console.log('🤖 Testing AI service connection...');
        await testAiConnection(firstSetting.id);
    }
    
    console.log('✅ Debug sequence complete! Check the logs above for any issues.');
}

// Instructions
console.log(`
🔧 AI SETTINGS DEBUG TOOLKIT LOADED

Available commands:
1. testAiSettingsModel() - Check if AI settings exist
2. createAiSettingsForConnector(1) - Create settings for connector 1
3. toggleAiSettings(settingId) - Toggle AI on/off
4. testAiConnection(settingId) - Test Django AI service
5. debugAiIntegration() - Run complete debug sequence

Quick start:
Run this command in console: debugAiIntegration()
`);

// Auto-run basic debug
console.log('🚀 Auto-running basic AI debug...');
testAiSettingsModel().then(settings => {
    if (settings && settings.length > 0) {
        console.log('✅ AI Settings working! You should see the toggle working now.');
    } else {
        console.log('⚠️ No AI settings found. Run: debugAiIntegration()');
    }
});
